#ifndef RKNN_TRACK_CONFIG_H__
#define RKNN_TRACK_CONFIG_H__

#include <cstdint>
#include <string>
#include <opencv2/opencv.hpp>
#include <rga/im2d.hpp>
#include <rga/rga.h>

// RKNN tracking configuration
struct RKNNTrackConfig {
    // Input configuration
    std::string input_topic_name = "main_video_frames";     // Input video topic name
    std::string input_transport_type = "DMA";          // Input transport type: DMA, SHMEM, FASTDDS
    int input_ring_buffer_size = 3;                    // Ring buffer size
    int input_width = 1920;                            // Input frame width
    int input_height = 1080;                           // Input frame height

    int fastdds_domain_id = 0;                         // FastDDS domain ID
    int fastdds_max_samples = 5;                       // FastDDS max samples

    // Output configuration
    std::string output_topic_name = "main_rknn_tracked_frames";  // Output topic name after tracking
    std::string output_transport_type = "DMA";             // Output transport type
    int output_ring_buffer_size = 5;                        // Ring buffer size
    int output_format = V4L2_PIX_FMT_BGR24;                // Output pixel format
    int output_width = 1920;                               // Output frame width
    int output_height = 1080;                              // Output frame height

    // RKNN model configuration
    std::string model_path = "/opt/models/yolo11s_rk3576.rknn";    // RKNN model file path
    std::string chip = "rk3576";                           // Chip type: rk3588, rk3576, etc
    
    // Tracking configuration
    float box_threshold = 0.5f;                            // Bounding box threshold
    float nms_threshold = 0.45f;                           // NMS threshold
    int track_buffer = 60;                                 // Track buffer size
    int frame_rate = 30;                                   // Frame rate for tracking
    
    // Output configuration
    bool draw_boxes = true;                                // Draw bounding boxes on output
    bool draw_labels = true;                               // Draw labels on output
    bool draw_track_ids = true;                            // Draw track IDs on output
    
    // Debug options
    int debug_level = 2;                                   // Debug level (0-5)
    int stats_interval = 10;                               // Statistics print interval (seconds)
};

#endif // RKNN_TRACK_CONFIG_H__