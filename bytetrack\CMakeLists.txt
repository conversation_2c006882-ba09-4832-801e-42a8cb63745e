cmake_minimum_required(VERSION 3.16)
project(bytetrack VERSION 1.0 LANGUAGES CXX)

# 收集源文件
file(GLOB SOURCES "src/*.cpp")

# 创建静态库
add_library(${PROJECT_NAME} STATIC ${SOURCES})

# 设置属性
set_target_properties(${PROJECT_NAME} PROPERTIES
    POSITION_INDEPENDENT_CODE ON
)

# 查找依赖
find_package(OpenCV REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(OpenMP)

# 设置编译器优化选项
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 启用OpenMP
if(OpenMP_CXX_FOUND)
    target_link_libraries(${PROJECT_NAME} PUBLIC OpenMP::OpenMP_CXX)
endif()

# 启用SIMD指令集优化 - 适配ARM和x86平台
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    # 检测目标架构
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm64|ARM64")
        # ARM64架构优化
        target_compile_options(${PROJECT_NAME} PRIVATE
            -O3
            -march=armv8.2-a+fp16+dotprod   # ARMv8.2-A with FP16 and DOT product
            -mtune=cortex-a76               # 针对Cortex-A76优化
            -ffast-math
            -funroll-loops
        )
    else()
        # x86_64架构优化
        target_compile_options(${PROJECT_NAME} PRIVATE
            -O3
            -march=native
            -mtune=native
            -ffast-math
            -funroll-loops
        )
    endif()
elseif(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
    target_compile_options(${PROJECT_NAME} PRIVATE
        /O2
        /arch:AVX2
        /fp:fast
    )
endif()

# 设置包含目录和链接库
target_include_directories(${PROJECT_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
)

target_link_libraries(${PROJECT_NAME}
    PUBLIC
        ${OpenCV_LIBS}
        Eigen3::Eigen
)

#include(GNUInstallDirs)
#
## 安装规则
#install(TARGETS ${PROJECT_NAME}
#    EXPORT ${PROJECT_NAME}Config
#    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
#    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
#    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
#    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
#)
#
#install(DIRECTORY include/
#    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/${PROJECT_NAME}
#    FILES_MATCHING PATTERN "*.h"
#)
#
#install(EXPORT ${PROJECT_NAME}Config
#    FILE ${PROJECT_NAME}Config.cmake
#    NAMESPACE ${PROJECT_NAME}::
#    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}
#)
#

