#ifndef V4L2_UTILS_H
#define V4L2_UTILS_H

#include "../include/common.h"
#include "../include/config/v4l2_capture_config.h"
#include <string>
#include <vector>
#include <linux/videodev2.h>


// V4L2格式信息
struct V4L2FormatInfo {
    uint32_t pixelformat;        // 像素格式
    uint32_t width;              // 宽度
    uint32_t height;             // 高度
    uint32_t numerator;          // 帧率分子
    uint32_t denominator;        // 帧率分母
    std::string description;     // 格式描述
};

// V4L2设备能力
struct V4L2DeviceCapability {
    bool supports_capture;       // 支持视频捕获
    bool supports_mplane;        // 支持多平面
    bool supports_dmabuf;        // 支持DMA缓冲区
    bool supports_userptr;       // 支持用户指针
    bool supports_streaming;     // 支持流传输
    std::string driver;          // 驱动名称
    std::string card;            // 设备名称
    uint32_t capabilities;       // 原始能力标志
};

// V4L2配置管理器
class V4L2ConfigManager {
public:
    // 查询设备能力
    static bool query_device_capability(const std::string& device, V4L2DeviceCapability& cap);
    
    // 枚举支持的格式
    static std::vector<V4L2FormatInfo> enumerate_formats(const std::string& device);
    
    // 查找最佳格式 - 兼容v4l2_capture_interface.h
    static bool find_optimal_format(const std::string& device, 
                                   const V4L2Capture::V4L2DeviceConfig& preferred,
                                   V4L2FormatInfo& selected);
    
    // 验证格式是否支持
    static bool validate_format(const std::string& device, const V4L2FormatInfo& format);
    
    // 获取格式描述
    static std::string get_format_description(uint32_t pixelformat);
    
    // 计算缓冲区大小
    static size_t calculate_buffer_size(const V4L2FormatInfo& format);
    
    // 兼容性辅助函数：从v4l2_capture_interface.h的配置创建查询参数
    struct QueryParams {
        std::string device_path;
        uint32_t width;
        uint32_t height;
        uint32_t pixel_format;
        uint32_t fps;
        
        QueryParams() : width(0), height(0), pixel_format(0), fps(0) {}
    };
    
    static QueryParams from_interface_config(const V4L2Capture::V4L2DeviceConfig& config);
    static bool find_optimal_format(const QueryParams& params, V4L2FormatInfo& selected);

private:
    // 内部辅助函数
    static bool open_device(const std::string& device, int& fd);
    static void close_device(int fd);
    static bool query_capability(int fd, V4L2DeviceCapability& cap);
    static bool enumerate_frame_sizes(int fd, uint32_t pixelformat, std::vector<V4L2FormatInfo>& formats);
    static bool enumerate_frame_rates(int fd, uint32_t pixelformat, uint32_t width, uint32_t height, 
                                     std::vector<V4L2FormatInfo>& formats);
};

// V4L2格式工具类
class V4L2FormatUtils {
public:
    // 像素格式转换
    static std::string pixelformat_to_string(uint32_t pixelformat);
    static uint32_t string_to_pixelformat(const std::string& format_str);
    
    // 格式分类
    static bool is_yuv_format(uint32_t pixelformat);
    static bool is_rgb_format(uint32_t pixelformat);
    static bool is_compressed_format(uint32_t pixelformat);
    static bool is_multiplanar_format(uint32_t pixelformat);
    
    // 计算相关
    static uint32_t get_bytes_per_pixel(uint32_t pixelformat);
    static uint32_t get_plane_count(uint32_t pixelformat);
    static size_t calculate_frame_size(uint32_t pixelformat, uint32_t width, uint32_t height);
    
    // 格式兼容性
    static bool is_format_compatible(uint32_t src_format, uint32_t dst_format);
    static std::vector<uint32_t> get_compatible_formats(uint32_t pixelformat);
};

// 常用格式定义
namespace V4L2Formats {
    // YUV格式
    constexpr uint32_t YUYV = V4L2_PIX_FMT_YUYV;
    constexpr uint32_t UYVY = V4L2_PIX_FMT_UYVY;
    constexpr uint32_t NV12 = V4L2_PIX_FMT_NV12;
    constexpr uint32_t NV21 = V4L2_PIX_FMT_NV21;
    constexpr uint32_t YUV420 = V4L2_PIX_FMT_YUV420;
    
    // RGB格式
    constexpr uint32_t RGB24 = V4L2_PIX_FMT_RGB24;
    constexpr uint32_t BGR24 = V4L2_PIX_FMT_BGR24;
    constexpr uint32_t RGB32 = V4L2_PIX_FMT_RGB32;
    constexpr uint32_t BGR32 = V4L2_PIX_FMT_BGR32;
    
    // 压缩格式
    constexpr uint32_t MJPEG = V4L2_PIX_FMT_MJPEG;
    constexpr uint32_t H264 = V4L2_PIX_FMT_H264;
    constexpr uint32_t H265 = V4L2_PIX_FMT_HEVC;
    
    // 灰度格式
    constexpr uint32_t GREY = V4L2_PIX_FMT_GREY;
}

// V4L2ConfigManager 实现
inline bool V4L2ConfigManager::query_device_capability(const std::string& device, V4L2DeviceCapability& cap) {
    int fd;
    if (!open_device(device, fd)) {
        return false;
    }

    bool result = query_capability(fd, cap);
    close_device(fd);
    return result;
}

inline std::vector<V4L2FormatInfo> V4L2ConfigManager::enumerate_formats(const std::string& device) {
    std::vector<V4L2FormatInfo> formats;
    int fd;

    if (!open_device(device, fd)) {
        return formats;
    }

    v4l2_fmtdesc fmt_desc;
    memset(&fmt_desc, 0, sizeof(fmt_desc));
    fmt_desc.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;

    for (fmt_desc.index = 0; ioctl(fd, VIDIOC_ENUM_FMT, &fmt_desc) >= 0; fmt_desc.index++) {
        enumerate_frame_sizes(fd, fmt_desc.pixelformat, formats);
    }

    close_device(fd);
    return formats;
}

inline bool V4L2ConfigManager::find_optimal_format(const std::string& device,
                                                   const V4L2Capture::V4L2DeviceConfig& preferred,
                                                   V4L2FormatInfo& selected) {
    (void) device;
    QueryParams params = from_interface_config(preferred);
    return find_optimal_format(params, selected);
}

inline bool V4L2ConfigManager::find_optimal_format(const QueryParams& params, V4L2FormatInfo& selected) {
    auto formats = enumerate_formats(params.device_path);
    if (formats.empty()) {
        return false;
    }

    // 实现原始的格式选择逻辑：优先级 format > fps > size
    V4L2FormatInfo best_match;
    bool found_match = false;

    for (const auto& fmt : formats) {
        // 1. 格式匹配（最高优先级）
        if (params.pixel_format != 0 && fmt.pixelformat != params.pixel_format) {
            continue;
        }

        // 2. 帧率匹配
        if (params.fps != 0) {
            uint32_t fmt_fps = fmt.denominator / fmt.numerator;
            if (fmt_fps != params.fps) {
                continue;
            }
        }

        // 3. 尺寸匹配（最低优先级）
        if (params.width != 0 && fmt.width != params.width) {
            continue;
        }
        if (params.height != 0 && fmt.height != params.height) {
            continue;
        }

        // 找到匹配的格式
        best_match = fmt;
        found_match = true;
        break;
    }

    if (!found_match) {
        // 如果没有完全匹配，选择第一个可用格式
        best_match = formats[0];
        LOG_W("No exact match found, using first available format: %s %dx%d@%d",
              get_format_description(best_match.pixelformat).c_str(),
              best_match.width, best_match.height,
              best_match.denominator / best_match.numerator);
    }

    selected = best_match;
    return true;
}

inline bool V4L2ConfigManager::open_device(const std::string& device, int& fd) {
    fd = open(device.c_str(), O_RDWR | O_NONBLOCK);
    return fd >= 0;
}

inline void V4L2ConfigManager::close_device(int fd) {
    if (fd >= 0) {
        close(fd);
    }
}

inline bool V4L2ConfigManager::query_capability(int fd, V4L2DeviceCapability& cap) {
    v4l2_capability v4l2_cap;
    if (ioctl(fd, VIDIOC_QUERYCAP, &v4l2_cap) < 0) {
        return false;
    }

    cap.supports_capture = (v4l2_cap.capabilities & V4L2_CAP_VIDEO_CAPTURE) != 0;
    cap.supports_mplane = (v4l2_cap.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) != 0;
    cap.supports_streaming = (v4l2_cap.capabilities & V4L2_CAP_STREAMING) != 0;
    cap.driver = reinterpret_cast<const char*>(v4l2_cap.driver);
    cap.card = reinterpret_cast<const char*>(v4l2_cap.card);
    cap.capabilities = v4l2_cap.capabilities;

    return true;
}

// V4L2FormatUtils 实现
inline std::string V4L2FormatUtils::pixelformat_to_string(uint32_t pixelformat) {
    char fourcc[5] = {0};
    fourcc[0] = pixelformat & 0xFF;
    fourcc[1] = (pixelformat >> 8) & 0xFF;
    fourcc[2] = (pixelformat >> 16) & 0xFF;
    fourcc[3] = (pixelformat >> 24) & 0xFF;
    return std::string(fourcc);
}

inline bool V4L2FormatUtils::is_yuv_format(uint32_t pixelformat) {
    switch (pixelformat) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
        case V4L2_PIX_FMT_YUV420:
            return true;
        default:
            return false;
    }
}

inline bool V4L2FormatUtils::is_rgb_format(uint32_t pixelformat) {
    switch (pixelformat) {
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
        case V4L2_PIX_FMT_RGB32:
        case V4L2_PIX_FMT_BGR32:
            return true;
        default:
            return false;
    }
}

inline bool V4L2FormatUtils::is_compressed_format(uint32_t pixelformat) {
    switch (pixelformat) {
        case V4L2_PIX_FMT_MJPEG:
        case V4L2_PIX_FMT_H264:
        case V4L2_PIX_FMT_HEVC:
            return true;
        default:
            return false;
    }
}

inline bool V4L2FormatUtils::is_multiplanar_format(uint32_t pixelformat) {
    switch (pixelformat) {
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
        case V4L2_PIX_FMT_YUV420:
            return true;
        default:
            return false;
    }
}

inline uint32_t V4L2FormatUtils::get_bytes_per_pixel(uint32_t pixelformat) {
    switch (pixelformat) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            return 2;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            return 3;
        case V4L2_PIX_FMT_RGB32:
        case V4L2_PIX_FMT_BGR32:
            return 4;
        case V4L2_PIX_FMT_GREY:
            return 1;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
        case V4L2_PIX_FMT_YUV420:
            return 1;  // 主平面
        default:
            return 1;
    }
}

inline uint32_t V4L2FormatUtils::get_plane_count(uint32_t pixelformat) {
    switch (pixelformat) {
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            return 2;
        case V4L2_PIX_FMT_YUV420:
            return 3;
        default:
            return 1;
    }
}

inline size_t V4L2FormatUtils::calculate_frame_size(uint32_t pixelformat, uint32_t width, uint32_t height) {
    uint32_t aligned_width = (width + 15) & ~15;
    uint32_t aligned_height = (height + 15) & ~15;
    switch (pixelformat) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            return aligned_width * aligned_height * 2;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            return aligned_width * aligned_height * 3;
        case V4L2_PIX_FMT_RGB32:
        case V4L2_PIX_FMT_BGR32:
            return aligned_width * aligned_height * 4;
        case V4L2_PIX_FMT_GREY:
            return aligned_width * aligned_height;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            return aligned_width * aligned_height * 3 / 2;
        case V4L2_PIX_FMT_YUV420:
            return aligned_width * aligned_height * 3 / 2;
        case V4L2_PIX_FMT_MJPEG:
        case V4L2_PIX_FMT_H264:
        case V4L2_PIX_FMT_HEVC:
            return aligned_width * aligned_height;  // 估算值
        default:
            return aligned_width * aligned_height * 2;  // 默认估算
    }
}

inline uint32_t V4L2FormatUtils::string_to_pixelformat(const std::string& format_str) {
    if (format_str.length() != 4) {
        return 0;
    }

    return (format_str[0]) | (format_str[1] << 8) | (format_str[2] << 16) | (format_str[3] << 24);
}

inline bool V4L2FormatUtils::is_format_compatible(uint32_t src_format, uint32_t dst_format) {
    // 简单的兼容性检查
    return src_format == dst_format;
}

inline std::vector<uint32_t> V4L2FormatUtils::get_compatible_formats(uint32_t pixelformat) {
    std::vector<uint32_t> compatible;
    compatible.push_back(pixelformat);

    // 添加一些常见的兼容格式
    if (is_yuv_format(pixelformat)) {
        compatible.push_back(V4L2_PIX_FMT_YUYV);
        compatible.push_back(V4L2_PIX_FMT_UYVY);
    } else if (is_rgb_format(pixelformat)) {
        compatible.push_back(V4L2_PIX_FMT_RGB24);
        compatible.push_back(V4L2_PIX_FMT_BGR24);
    }

    return compatible;
}

// 添加缺失的私有函数实现
inline bool V4L2ConfigManager::enumerate_frame_sizes(int fd, uint32_t pixelformat, std::vector<V4L2FormatInfo>& formats) {
    v4l2_frmsizeenum frmsize;
    memset(&frmsize, 0, sizeof(frmsize));
    frmsize.pixel_format = pixelformat;

    for (frmsize.index = 0; ioctl(fd, VIDIOC_ENUM_FRAMESIZES, &frmsize) >= 0; frmsize.index++) {
        if (frmsize.type == V4L2_FRMSIZE_TYPE_DISCRETE) {
            enumerate_frame_rates(fd, pixelformat, frmsize.discrete.width, frmsize.discrete.height, formats);
        } else if (frmsize.type == V4L2_FRMSIZE_TYPE_STEPWISE) {
            // 对于步进式，选择几个常用分辨率
            uint32_t widths[] = {640, 1280, 1920};
            uint32_t heights[] = {480, 720, 1080};

            for (int i = 0; i < 3; i++) {
                if (widths[i] >= frmsize.stepwise.min_width && widths[i] <= frmsize.stepwise.max_width &&
                    heights[i] >= frmsize.stepwise.min_height && heights[i] <= frmsize.stepwise.max_height) {
                    enumerate_frame_rates(fd, pixelformat, widths[i], heights[i], formats);
                }
            }
        }
    }

    return true;
}

inline bool V4L2ConfigManager::enumerate_frame_rates(int fd, uint32_t pixelformat, uint32_t width, uint32_t height,
                                                    std::vector<V4L2FormatInfo>& formats) {
    v4l2_frmivalenum frmival;
    memset(&frmival, 0, sizeof(frmival));
    frmival.pixel_format = pixelformat;
    frmival.width = width;
    frmival.height = height;

    bool found_rates = false;
    for (frmival.index = 0; ioctl(fd, VIDIOC_ENUM_FRAMEINTERVALS, &frmival) >= 0; frmival.index++) {
        V4L2FormatInfo info;
        info.pixelformat = pixelformat;
        info.width = width;
        info.height = height;
        info.description = get_format_description(pixelformat);

        if (frmival.type == V4L2_FRMIVAL_TYPE_DISCRETE) {
            info.numerator = frmival.discrete.numerator;
            info.denominator = frmival.discrete.denominator;
        } else {
            // 对于连续或步进式，使用默认值
            info.numerator = 1;
            info.denominator = 30;
        }

        formats.push_back(info);
        found_rates = true;
    }

    // 如果没有找到帧率信息，添加默认帧率
    if (!found_rates) {
        V4L2FormatInfo info;
        info.pixelformat = pixelformat;
        info.width = width;
        info.height = height;
        info.numerator = 1;
        info.denominator = 30;
        info.description = get_format_description(pixelformat);
        formats.push_back(info);
    }

    return true;
}

// 兼容性辅助函数实现
inline V4L2ConfigManager::QueryParams V4L2ConfigManager::from_interface_config(const V4L2Capture::V4L2DeviceConfig& config) {
    QueryParams params;
    params.device_path = config.device_path;
    params.width = config.width;
    params.height = config.height;
    params.pixel_format = config.pixel_format;
    params.fps = config.fps;
    return params;
}

inline bool V4L2ConfigManager::validate_format(const std::string& device, const V4L2FormatInfo& format) {
    auto formats = enumerate_formats(device);

    for (const auto& fmt : formats) {
        if (fmt.pixelformat == format.pixelformat &&
            fmt.width == format.width &&
            fmt.height == format.height) {
            return true;
        }
    }

    return false;
}

inline std::string V4L2ConfigManager::get_format_description(uint32_t pixelformat) {
    return V4L2FormatUtils::pixelformat_to_string(pixelformat);
}

inline size_t V4L2ConfigManager::calculate_buffer_size(const V4L2FormatInfo& format) {
    return V4L2FormatUtils::calculate_frame_size(format.pixelformat, format.width, format.height);
}

#endif // V4L2_UTILS_H
