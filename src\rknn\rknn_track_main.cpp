#include "../include/common.h"
#include "../include/config/rknn_track_config.h"
#include "../include/transport/video_transport_interface.h"
#include "../include/transport/fastdds_video_transport.h"
#include "../include/transport/shared_buffer_publisher.h"
#include "../include/rknn/yolo11_track.h"
#include "../include/hardware/rga_accelerator.h"
#include "../include/hardware/mpp_decoder.h"
#include "../include/capture/v4l2_utils.h"
#include <opencv2/opencv.hpp>
#include <opencv2/imgcodecs.hpp>
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <fstream>
#include <chrono>
#include <thread>
#include <json/json.h>
#include <atomic>
#include <unordered_map>
#include <malloc.h>

using namespace tracking;

// Define global variable class_names
std::vector<char *> class_names;
// 全局变量
static std::unique_ptr<yolo11_track> g_yolo11_track;
static std::unique_ptr<video_transport::IVideoSubscriber> g_subscriber;
static std::unique_ptr<video_transport::IVideoPublisher> g_publisher;
static std::atomic<bool> g_shutdown_requested{false};
static RKNNTrackConfig g_config; // 全局配置变量
static std::unordered_map<void *, video_transport::IVideoPublisher::PublisherData> g_publisher_buffers;
static std::unique_ptr<MPPDecoder> g_decoder;

void signal_handler(int signal) {
    LOG_I("Received signal %d, shutting down...", signal);
    g_shutdown_requested.store(true);
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "  -c, --config FILE          Configuration file\n"
              << "  -i, --input-topic TOPIC    Input topic (default: main_video_frames)\n"
              << "  -o, --output-topic TOPIC   Output topic (default: main_rknn_tracked_frames)\n"
              << "  --input-transport TYPE     Input transport type (default: DMA)\n"
              << "  --output-transport TYPE    Output transport type (default: DMA)\n"
              << "  -m, --model PATH           RKNN model file path\n"
              << "  --chip TYPE                Chip type (default: rk3576)\n"
              << "  --help                     Show help\n";
}

bool load_config_from_json(const std::string& config_file, RKNNTrackConfig& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) return false;

    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;

    if (!Json::parseFromStream(builder, file, &root, &errors)) return false;

    // complete loading config parameters
    if (root.isMember("input_topic_name")) config.input_topic_name = root["input_topic_name"].asString();
    if (root.isMember("output_topic_name")) config.output_topic_name = root["output_topic_name"].asString();
    if (root.isMember("model_path")) config.model_path = root["model_path"].asString();
    if (root.isMember("chip")) config.chip = root["chip"].asString();
    if (root.isMember("box_threshold")) config.box_threshold = root["box_threshold"].asFloat();
    if (root.isMember("nms_threshold")) config.nms_threshold = root["nms_threshold"].asFloat();
    if (root.isMember("track_buffer")) config.track_buffer = root["track_buffer"].asInt();
    if (root.isMember("frame_rate")) config.frame_rate = root["frame_rate"].asInt();
    if (root.isMember("draw_boxes")) config.draw_boxes = root["draw_boxes"].asBool();
    if (root.isMember("draw_labels")) config.draw_labels = root["draw_labels"].asBool();
    if (root.isMember("draw_track_ids")) config.draw_track_ids = root["draw_track_ids"].asBool();
    if (root.isMember("debug_level")) config.debug_level = root["debug_level"].asInt();
    if (root.isMember("stats_interval")) config.stats_interval = root["stats_interval"].asInt();
    if (root.isMember("input_transport_type")) config.input_transport_type = root["input_transport_type"].asString();
    if (root.isMember("output_transport_type")) config.output_transport_type = root["output_transport_type"].asString();
    if (root.isMember("input_width")) config.input_width = root["input_width"].asInt();
    if (root.isMember("input_height")) config.input_height = root["input_height"].asInt();
    if (root.isMember("output_width")) config.output_width = root["output_width"].asInt();
    if (root.isMember("output_height")) config.output_height = root["output_height"].asInt();
    if (root.isMember("output_format")) config.output_format = V4L2FormatUtils::string_to_pixelformat(root["output_format"].asString());

    return true;
}

void print_config(const RKNNTrackConfig& config) {
    LOG_I("=== RKNN Track Configuration ===");
    LOG_I("Input Topic: %s", config.input_topic_name.c_str());
    LOG_I("Output Topic: %s", config.output_topic_name.c_str());
    LOG_I("Model Path: %s", config.model_path.c_str());
    LOG_I("Chip: %s", config.chip.c_str());
    LOG_I("Box Threshold: %f", config.box_threshold);
    LOG_I("NMS Threshold: %f", config.nms_threshold);
    LOG_I("Track Buffer: %d", config.track_buffer);
    LOG_I("Frame Rate: %d", config.frame_rate);
    LOG_I("Draw Boxes: %s", config.draw_boxes ? "yes" : "no");
    LOG_I("Draw Labels: %s", config.draw_labels ? "yes" : "no");
    LOG_I("Draw Track IDs: %s", config.draw_track_ids ? "yes" : "no");
    LOG_I("Debug Level: %d", config.debug_level);
    LOG_I("Stats Interval: %d", config.stats_interval);
    LOG_I("Input Transport: %s", config.input_transport_type.c_str());
    LOG_I("Output Transport: %s", config.output_transport_type.c_str());
    LOG_I("Output Format: %s", V4L2FormatUtils::pixelformat_to_string(config.output_format).c_str());
    LOG_I("Input Width: %d", config.input_width);
    LOG_I("Input Height: %d", config.input_height);
    LOG_I("Output Width: %d", config.output_width);
    LOG_I("Output Height: %d", config.output_height);
    LOG_I("FastDDS Domain ID: %d", config.fastdds_domain_id);
    LOG_I("FastDDS Max Samples: %d", config.fastdds_max_samples);
    LOG_I("=================================");
}

// 跟踪回调函数
void tracking_callback(cv::Mat& frame, std::vector<TrackObj>& track_objs) {    
    // 根据配置绘制跟踪结果
    // 使用全局配置
    extern RKNNTrackConfig g_config;
    
    if (g_config.draw_boxes || g_config.draw_labels || g_config.draw_track_ids) {
        for (const auto& obj : track_objs) {
            // 绘制边界框
            if (g_config.draw_boxes) {
                cv::rectangle(frame,
                    cv::Point(obj.box.x, obj.box.y),
                    cv::Point(obj.box.x + obj.box.width, obj.box.y + obj.box.height),
                    cv::Scalar(0, 255, 0), 2);
            }
            
            // 绘制标签和跟踪ID
            if (g_config.draw_labels || g_config.draw_track_ids) {
                std::string label = "";
                if (g_config.draw_labels && obj.label) {
                    label += obj.label;
                }
                if (g_config.draw_track_ids) {
                    if (!label.empty()) label += " ";
                    label += "ID:" + std::to_string(obj.track_id);
                }
                
                if (!label.empty()) {
                    cv::Point text_pos(obj.box.x, obj.box.y - 5);
                    if (text_pos.y < 0) text_pos.y = obj.box.y + obj.box.height + 15;
                    
                    int baseLine = 0;
                    cv::Size label_size = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);
                    
                    // 绘制标签背景
                    cv::rectangle(frame,
                        cv::Point(text_pos.x, text_pos.y - label_size.height),
                        cv::Point(text_pos.x + label_size.width, text_pos.y + baseLine),
                        cv::Scalar(0, 0, 0), cv::FILLED);
                    
                    // 绘制标签文本
                    cv::putText(frame, label, text_pos, cv::FONT_HERSHEY_SIMPLEX,
                        0.5, cv::Scalar(255, 255, 255), 1);
                }
            }
        }
    }
    
    // 将处理后的帧发布到输出主题
    if (g_publisher) {
        // 从map中获取发布缓冲区数据结构
        auto it = g_publisher_buffers.find(frame.data);
        if (it == g_publisher_buffers.end()) {
            LOG_E("Failed to find publisher buffer for frame");
            return;
        }
        video_transport::IVideoPublisher::PublisherData pub_data = it->second;

        if (pub_data.data_ptr != frame.data) {
            LOG_E("Publisher buffer data pointer does not match frame data pointer");
            return;
        }
        // 设置元数据
        LOG_I("[Publisher] frame id: %ld, delay: %ld ms, track objs: %zu", pub_data.meta.frame_id, (get_current_us() - pub_data.meta.timestamp)/1000, track_objs.size());   
        pub_data.meta.width = frame.cols;
        pub_data.meta.height = frame.rows;
        pub_data.meta.format = g_config.output_format; // output format
        pub_data.meta.data_size = frame.total() * frame.elemSize();
        pub_data.meta.is_valid = true;
        pub_data.meta.is_keyframe = true;
        // 发布帧数据到输出主题
        g_publisher->publish_buffer(pub_data);
        // 释放发布缓冲区数据结构
        g_publisher_buffers.erase(it);
    }
}
// #define USE_MPP_DECODER 1
bool mpp_decode_mjpeg(video_transport::IVideoSubscriber::SubscriberData & sub_data, cv::Mat& frame) {

    if (!g_decoder) { // 第一次初始化解码器，使用V4L2格式创建解码器。
        g_decoder = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG); // 兼容V4L2格式的构造函数。
        if (!g_decoder->init(sub_data.meta.width, sub_data.meta.height, V4L2_PIX_FMT_NV12)) { // RK_MPP + rk3576 只支持解码为YUV
            LOG_E("[Subscriber] Failed to initialize MPP decoder");
            return true;                        
        }
    }
    if (!g_decoder->is_initialized() || !g_decoder->decode_frame(sub_data.data_ptr, sub_data.meta.data_size, [&](void* ptr, size_t size, int32_t width, int32_t height, int32_t hor_stride, int32_t ver_stride) {
        // 使用RGA将YUV420_SP转换为BGR格式
        if (!RGAAccelerator::color_convert(ptr, width, height, hor_stride, ver_stride, V4L2_PIX_FMT_NV12, frame.data, g_config.output_format)) {
            LOG_E("[Subscriber] Format conversion failed");
            return;
        }
        LOG_I("[Subscriber] MPP decode success, width: %d, height: %d, size: %zu", width, height, size);
    })) {
        LOG_E("[Subscriber] MPP decode failed");
        return false;
    }
    return true;
}

int main(int argc, char* argv[]) {
    Logger::set_level(LEVEL_INFO);
    
    RKNNTrackConfig config;
    std::string config_file;
    
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"input-topic", required_argument, 0, 'i'},
        {"output-topic", required_argument, 0, 'o'},
        {"input-transport", required_argument, 0, 2},
        {"output-transport", required_argument, 0, 3},
        {"model", required_argument, 0, 'm'},
        {"chip", required_argument, 0, 1},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;

    load_config_from_json("/opt/video_service/config/main_rknn_track.json", config);

    // 第一次解析：只获取配置文件路径
    optind = 1;
    while ((c = getopt_long(argc, argv, "c:i:o:m:h", long_options, &option_index)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件
    if (!config_file.empty()) {
        if (!load_config_from_json(config_file, config)) {
            LOG_E("Failed to load configuration file");
            return 1;
        }
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1;
    option_index = 0;
    while ((c = getopt_long(argc, argv, "c:i:o:m:h", long_options, &option_index)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 'i':
                config.input_topic_name = optarg;
                break;
            case 'o':
                config.output_topic_name = optarg;
                break;
            case 'm':
                config.model_path = optarg;
                break;
            case 1: // --chip
                config.chip = optarg;
                break;
            case 2: // --input-transport
                config.input_transport_type = optarg;
                break;
            case 3: // --output-transport
                config.output_transport_type = optarg;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            case '?':
                print_usage(argv[0]);
                return 1;
            default:
                break;
        }
    }

    // 打印配置
    print_config(config);

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 创建输入订阅者
    video_transport::TransportConfig subscriber_config;
    subscriber_config.topic_name = config.input_topic_name;
    if (config.input_transport_type == "SHMEM") {
        subscriber_config.type = video_transport::TransportType::SHMEM;
    } else if (config.input_transport_type == "FASTDDS") {
        subscriber_config.type = video_transport::TransportType::FASTDDS;
    } else {
        subscriber_config.type = video_transport::TransportType::DMA;
    }

    if (subscriber_config.type == video_transport::TransportType::FASTDDS) {
        subscriber_config.domain_id = config.fastdds_domain_id;
        subscriber_config.max_samples = config.fastdds_max_samples;
        subscriber_config.timeout_ms = 1000;
    } else {
        subscriber_config.ring_buffer_size = config.input_ring_buffer_size;
        subscriber_config.timeout_ms = 1000;
    }

    LOG_I("Creating subscriber for %s using %s transport", 
          config.input_topic_name.c_str(), config.input_transport_type.c_str());

    g_subscriber = video_transport::VideoTransportFactory::create_subscriber(subscriber_config);
    if (!g_subscriber) {
        LOG_E("Failed to create subscriber for %s", config.input_topic_name.c_str());
        return 1;
    }

    if (!g_subscriber->initialize()) {
        LOG_E("Failed to initialize subscriber");
        return 1;
    }

    // 创建输出发布者
    video_transport::TransportConfig publisher_config;
    publisher_config.topic_name = config.output_topic_name;
    if (config.output_transport_type == "SHMEM") {
        publisher_config.type = video_transport::TransportType::SHMEM;
    } else if (config.output_transport_type == "FASTDDS") {
        publisher_config.type = video_transport::TransportType::FASTDDS;
    } else {
        publisher_config.type = video_transport::TransportType::DMA;
    }

    if (publisher_config.type == video_transport::TransportType::FASTDDS) {
        publisher_config.domain_id = config.fastdds_domain_id;
        publisher_config.max_samples = config.fastdds_max_samples;
        publisher_config.timeout_ms = 1000;
    } else {
        publisher_config.buffer_size = V4L2FormatUtils::calculate_frame_size(config.output_format, config.output_width, config.output_height);
        publisher_config.ring_buffer_size = config.output_ring_buffer_size;
        publisher_config.timeout_ms = 1000;
    }

    LOG_I("Creating publisher for %s using %s transport", 
          config.output_topic_name.c_str(), config.output_transport_type.c_str());

    g_publisher = video_transport::VideoTransportFactory::create_publisher(publisher_config);
    if (!g_publisher) {
        LOG_E("Failed to create publisher for %s", config.output_topic_name.c_str());
        return 1;
    }

    if (!g_publisher->initialize()) {
        LOG_E("Failed to initialize publisher");
        return 1;
    }

    // 保存配置到全局变量
    g_config = config;
    
    // 创建RKNN跟踪器
    g_yolo11_track = std::make_unique<yolo11_track>(
        config.chip,
        config.model_path,
        config.frame_rate,
        config.track_buffer,
        config.box_threshold,
        config.nms_threshold,
        tracking_callback
    );

    if (!g_yolo11_track) {
        LOG_E("Failed to create yolo11_track");
        return 1;
    }

    LOG_I("RKNN tracking service is running. Press Ctrl+C to stop");

    // 主循环
    while (!g_shutdown_requested.load()) {
        video_transport::IVideoSubscriber::SubscriberData sub_data;
        uint64_t start_ts = get_current_us();
        if (g_subscriber->receive_frame_buffer(sub_data, 1000) != video_transport::BufferResult::SUCCESS) {
            continue;
        }
        if (!sub_data.data_ptr || !sub_data.meta.is_valid) {
            continue;
        }
        
        video_transport::IVideoPublisher::PublisherData pub_data; // 发布缓冲区数据结构
        uint64_t rx_ts = get_current_us();
        try {
            if (g_publisher->acquire_buffer(pub_data) == video_transport::BufferResult::SUCCESS) {
                uint64_t acquire_ts = get_current_us();
                pub_data.meta.is_valid = false;
                pub_data.meta.frame_id = sub_data.meta.frame_id;
                pub_data.meta.timestamp = sub_data.meta.timestamp; // 记录接收时间戳，用于计算延迟时间。
                // 将接收到的帧转换为OpenCV Mat, 使用发布缓冲区的内存
                cv::Mat frame(sub_data.meta.height, sub_data.meta.width, CV_8UC3, pub_data.data_ptr);
                if (sub_data.meta.format == V4L2_PIX_FMT_MJPEG || sub_data.meta.format == V4L2_PIX_FMT_JPEG) { // 解码MJPEG帧为BGR格式。
#if USE_MPP_DECODER
                    if (!mpp_decode_mjpeg(sub_data, frame)) { // 解码MJPEG帧为BGR格式。
                        g_publisher->publish_buffer(pub_data, false);                   // 发布失败，将发布缓冲区数据结构还回池中。                    
                        continue; // 继续下一帧。                        
                    }
#else
                    cv::imdecode(cv::Mat(1, sub_data.meta.data_size, CV_8UC1, sub_data.data_ptr), cv::IMREAD_COLOR, &frame);
#endif // USE_MPP_DECODER                    
                } else {
                    // 进行格式转换
                    if (!RGAAccelerator::color_convert(sub_data.data_ptr, sub_data.meta.width, sub_data.meta.height, sub_data.meta.width, sub_data.meta.height, sub_data.meta.format, frame.data, g_config.output_format)) { // output format                        
                        LOG_E("[Subscriber] Format conversion failed");
                        g_publisher->publish_buffer(pub_data, false);                   // 发布失败，将发布缓冲区数据结构还回池中。                    
                        continue; // 继续下一帧。                        
                    }
                }
                // 保存发布缓冲区数据结构到map中
                g_publisher_buffers[frame.data] = pub_data;
                uint64_t convert_ts = get_current_us();
                // 推送到RKNN进行跟踪
                g_yolo11_track->push_frame(frame);
                LOG_I("[Subscriber] Frame id: %ld, cost: %ld ms, rx: %ld ms, convert: %ld ms", pub_data.meta.frame_id, (get_current_us() - start_ts) / 1000, (rx_ts - start_ts) / 1000, (convert_ts - acquire_ts) / 1000);                    
            }
        } catch (const std::exception& e) {
            LOG_E("[Subscriber] acquire_buffer Exception: %s", e.what());
        }
    }

    LOG_I("RKNN tracking service is stopping...");
    // 清理
    g_yolo11_track->stop(); // 停止跟踪器。
    g_yolo11_track.reset(); // 销毁跟踪器。
    if (g_decoder) { // 销毁解码器。
        g_decoder->cleanup();
    }
    if (g_publisher) {
        g_publisher->cleanup();
    }
    if (g_subscriber) {
        g_subscriber->cleanup();
    }

    LOG_I("RKNN tracking service shutdown complete");
    return 0;
}
