#!/bin/bash

# RTSP服务器播放卡住问题修复测试脚本
# 用于验证修复效果

echo "=== RTSP服务器播放卡住问题修复测试 ==="

# 检查是否有正在运行的RTSP服务器
echo "1. 检查现有RTSP服务器进程..."
ps aux | grep rtsp_server_main | grep -v grep

# 编译修复后的代码
echo "2. 编译修复后的代码..."
cd build
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "编译失败，请检查代码"
    exit 1
fi

echo "3. 编译成功"

# 启动RTSP服务器（后台运行）
echo "4. 启动RTSP服务器..."
./src/streaming/rtsp_server_main --topic video_frames --port 8554 --mount /stream --gst-debug 3 > rtsp_server_test.log 2>&1 &
RTSP_PID=$!

echo "RTSP服务器已启动，PID: $RTSP_PID"
echo "日志文件: rtsp_server_test.log"

# 等待服务器启动
sleep 3

# 检查服务器是否正常运行
if ps -p $RTSP_PID > /dev/null; then
    echo "5. RTSP服务器运行正常"
    echo "RTSP URL: rtsp://localhost:8554/stream"
    
    # 提供测试建议
    echo ""
    echo "=== 测试建议 ==="
    echo "1. 使用VLC播放器测试: vlc rtsp://localhost:8554/stream"
    echo "2. 使用ffplay测试: ffplay rtsp://localhost:8554/stream"
    echo "3. 使用gst-launch测试:"
    echo "   gst-launch-1.0 rtspsrc location=rtsp://localhost:8554/stream ! decodebin ! autovideosink"
    echo ""
    echo "观察是否还会出现播放几帧后卡住的问题"
    echo ""
    echo "停止服务器: kill $RTSP_PID"
    echo "查看日志: tail -f rtsp_server_test.log"
else
    echo "5. RTSP服务器启动失败"
    echo "查看错误日志:"
    cat rtsp_server_test.log
    exit 1
fi

echo ""
echo "=== 修复说明 ==="
echo "本次修复主要解决了以下问题："
echo "1. 数据流中断时的处理机制"
echo "2. 增加了最后一帧重复发送功能"
echo "3. 优化了appsrc缓冲配置"
echo "4. 改进了超时处理机制"
echo ""
echo "如果问题仍然存在，请检查："
echo "1. 视频数据源是否稳定"
echo "2. 网络连接是否正常"
echo "3. 客户端播放器设置"
