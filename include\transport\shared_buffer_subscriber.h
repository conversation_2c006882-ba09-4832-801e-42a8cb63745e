#ifndef SHARED_BUFFER_SUBSCRIBER_H
#define SHARED_BUFFER_SUBSCRIBER_H

#include <liburing.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <atomic>
#include <vector>
#include <unordered_map>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <cstring>
#include <chrono>
#include <queue>
#include <functional>
#include <sstream>
#include "shared_buffer_manager.h"
#include "video_transport_interface.h"
#include "../common.h"

namespace video_transport {

// 前向声明
class SharedBufferSubscriber;

// 消费者客户端 - 实现 IVideoSubscriber 接口
class SharedBufferSubscriber : public IVideoSubscriber {
public:

    struct MappedBufferInfo {
        void* addr;
        size_t size;
        int fd;
        MappedBufferInfo() : addr(nullptr), size(0), fd(-1) {}
        MappedBufferInfo(void* a, size_t s, int f) : addr(a), size(s), fd(f) {}
    };

    struct SyncBuffer {
        TransportData data;
        void* addr;
        int fd;
        SyncBuffer() : addr(nullptr), fd(-1) {}
        SyncBuffer(const TransportData & d, void* a, int f) : data(d), addr(a), fd(f) {}
    };
    
    // SharedBuffer操作数据结构
    struct OperationData {
        enum Type { RECV, SEND } type;
        Message* msg;

        OperationData(Type t, Message* m) : type(t), msg(m) {}
    };

    // 默认构造函数，用于初始化订阅者对象。
    SharedBufferSubscriber(const TransportConfig& config) : IVideoSubscriber(config), socket_path_(config.topic_name), server_fd_(-1), running_(false), initialized_(false), connected_(false) {}
    // 默认析构函数，用于清理订阅者对象。
    ~SharedBufferSubscriber() override { cleanup(); }

    // IVideoSubscriber接口实现
    bool initialize() override;
    void cleanup() override;
    BufferResult receive_frame_buffer(SubscriberData& data, int timeout_ms = 1000) override;
    void set_buffer_callback(std::function<void(SubscriberData&)> callback) override;
    bool is_connected() const override;
    TransportStats get_stats() const override;
    void reset_stats() override;
    std::string get_status() const override;

private:
    void setup_io_uring();
    void connect_to_server();  // 连接到服务器，如果连接失败，则会自动重试。
    bool try_connect_to_server();  // 尝试连接到服务器，如果连接失败，返回false。
    void submit_recv();
    void submit_release(uint64_t buffer_id);
    void process_events();
    void handle_recv(struct io_uring_cqe* cqe, OperationData* data);
    void handle_send(struct io_uring_cqe* cqe, OperationData* data);
    void update_stats_received(size_t bytes, bool success);

    // 成员变量
    std::string socket_path_;
    int server_fd_;
    std::atomic<bool> running_;
    std::atomic<bool> initialized_;
    std::atomic<bool> connected_;
    io_uring ring_;

    std::thread event_thread_;
    mutable std::mutex stats_mutex_;
    TransportStats stats_;
    std::function<void(SubscriberData&)> buffer_callback_;

    // 双缓冲机制 - 单个读缓冲区和单个写缓冲区交替使用
    struct DoubleBuffer {
        std::unique_ptr<SyncBuffer> read_buffer;   // 读缓冲区 - 消费者使用
        std::unique_ptr<SyncBuffer> write_buffer;  // 写缓冲区 - 生产者使用
        std::mutex read_mutex;                     // 读缓冲区互斥锁
        std::mutex write_mutex;                    // 写缓冲区互斥锁
        std::condition_variable read_cv;           // 读缓冲区条件变量
        std::atomic<bool> write_buffer_ready{false};  // 写缓冲区就绪标志
        
        DoubleBuffer() = default;
        
        // 交换读写缓冲区， 置空写缓冲区，通知消费者有新数据可用。（生产者调用）
        void swap_buffers() {
            std::lock_guard<std::mutex> read_lock(read_mutex);
            std::lock_guard<std::mutex> write_lock(write_mutex);
            if (write_buffer) {
                read_buffer = std::move(write_buffer);
                write_buffer_ready.store(false);
            } else if (read_buffer) {
                read_buffer.reset();
            }
            read_cv.notify_all();
        }

        // 设置写缓冲区（生产者调用）
        void set_write_buffer(SyncBuffer && new_buffer) {
            std::lock_guard<std::mutex> lock(write_mutex);
            write_buffer = std::make_unique<SyncBuffer>(std::move(new_buffer)); // 移动构造函数，将new_buffer中的数据移动到write_buffer中。
            write_buffer_ready.store(true);
            read_cv.notify_all();
        }
    
        // 检查读缓冲区是否有数据（线程安全）
        bool has_read_buffer() {
            std::lock_guard<std::mutex> lock(read_mutex);
            return read_buffer != nullptr;
        }
        // 检查写缓冲区是否有数据（线程安全）
        bool has_write_buffer() {
            std::lock_guard<std::mutex> lock(write_mutex);
            return write_buffer != nullptr;
        }
    };
    
    DoubleBuffer double_buffer_;

    std::unordered_map<uint64_t, MappedBufferInfo> mapped_buffers_;
    std::mutex mapped_buffers_mutex_;
};

// ========================================
// SharedBufferSubscriber 实现
// ========================================

inline bool SharedBufferSubscriber::initialize() {
    if (initialized_.load()) {
        LOG_I("SharedBufferSubscriber already initialized");
        return true;
    }

    if (config_.type != TransportType::DMA && config_.type != TransportType::SHMEM) {
        LOG_E("Invalid config type for SharedBuffer transport"); 
        return false;
    }
    
    try {
        connect_to_server();  // 尝试连接到服务器，如果连接失败，则会自动重试。
        setup_io_uring();
        connected_.store(true);
        initialized_.store(true);
        running_.store(true);
        event_thread_ = std::thread(&SharedBufferSubscriber::process_events, this);
        submit_recv();
        LOG_I("SharedBufferSubscriber type: %s initialized successfully on socket : %s", 
              config_.type == TransportType::DMA ? "DMA" : "SHMEM", 
              config_.topic_name.c_str());
        return true;
    } catch (const std::exception& e) {
        LOG_E("Failed to initialize SharedBufferSubscriber: %s", e.what());
        cleanup();
        return false;
    }
}

inline void SharedBufferSubscriber::cleanup() {
    if (!initialized_.load()) {
        return;
    }

    running_.store(false);  // 设置运行标志为false，停止事件循环。
    connected_.store(false);

    // 唤醒等待的线程
    double_buffer_.read_cv.notify_all();

    if (event_thread_.joinable()) {
        struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
        if (sqe) {
            io_uring_prep_nop(sqe);
            io_uring_submit(&ring_);
            event_thread_.join();
        }
    }

    if (server_fd_ >= 0) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // 清理所有持久映射的缓冲区 - 只有断开时才munmap
    {
        std::lock_guard<std::mutex> lock(mapped_buffers_mutex_);
        for (auto& [buffer_id, info] : mapped_buffers_) {
            if (info.addr && info.addr != MAP_FAILED) {
                munmap(info.addr, info.size);
            }
            if (info.fd >= 0) {
                close(info.fd);
            }
        }
        mapped_buffers_.clear();
    }

    io_uring_queue_exit(&ring_);
    initialized_.store(false);
    LOG_I("SharedBufferSubscriber type: %s cleaned up successfully", config_.type == TransportType::DMA ? "DMA" : "SHMEM");
}

inline BufferResult SharedBufferSubscriber::receive_frame_buffer(SubscriberData& sub_data, int timeout_ms) {
    if (!connected_.load()) {
        return BufferResult::TRANSPORT_ERROR;
    }

    if (!double_buffer_.has_read_buffer()) {
        if (!double_buffer_.write_buffer_ready.load()) {
            std::unique_lock<std::mutex> lock(double_buffer_.write_mutex);
            double_buffer_.read_cv.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                                                  [this]() {
                                                      return double_buffer_.write_buffer_ready.load() || !connected_.load();
                                                  });
            lock.unlock();
            // 交换读写缓冲区
            if (double_buffer_.write_buffer_ready.load()) {
                double_buffer_.swap_buffers();
            }
        } else {
            // 交换读写缓冲区， 置空写缓冲区，通知消费者有新数据可用。（生产者调用）
            double_buffer_.swap_buffers();
        }
    }

    if (!double_buffer_.has_read_buffer()) {
        return BufferResult::TIMEOUT;
    }

    // 获取读缓冲区中的数据
    auto sync_buffer = std::move(double_buffer_.read_buffer);  // 移动数据

    // 设置SubscriberData字段
    sub_data.meta = sync_buffer->data.meta;  // 复制TransportMetadata
    sub_data.data_ptr = sync_buffer->addr;  // 复制数据指针
    sub_data.buffer_id = sync_buffer->data.buffer_id;  // 复制缓冲区ID
    sub_data.fd = sync_buffer->fd;  // 复制缓冲区ID
    sub_data.transport_type = config_.type;  // 设置传输类型
    // LOG_I("Notice frame arrived, buffer id: %d, frame id: %ld, delay: %ld ms", sub_data.buffer_id, sub_data.meta.frame_id, (get_current_us() - sub_data.meta.timestamp)/1000);

    return BufferResult::SUCCESS;
}

inline void SharedBufferSubscriber::set_buffer_callback(std::function<void(SubscriberData&)> callback) {
    buffer_callback_ = std::move(callback);
}

inline bool SharedBufferSubscriber::is_connected() const {
    return connected_.load();
}

inline TransportStats SharedBufferSubscriber::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

inline void SharedBufferSubscriber::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = TransportStats();
}

inline std::string SharedBufferSubscriber::get_status() const {
    std::ostringstream oss;
    oss << "SharedBufferSubscriber: "
        << (initialized_.load() ? "initialized" : "not initialized")
        << ", connected: " << (connected_.load() ? "yes" : "no")
        << ", socket: " << socket_path_
        << ", mapped_buffers: " << mapped_buffers_.size();
    return oss.str();
}

// ========================================
// 私有方法实现
// ========================================

inline void SharedBufferSubscriber::setup_io_uring() {
    struct io_uring_params params = {};
    if (io_uring_queue_init_params(256, &ring_, &params) != 0) {
        throw std::runtime_error("Failed to initialize io_uring");
    }
}

inline bool SharedBufferSubscriber::try_connect_to_server() {
    server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
    if (server_fd_ < 0) {
        return false;
    }
    
    // 设置非阻塞模式
    int flags = fcntl(server_fd_, F_GETFL, 0);
    if (flags >= 0) {
        fcntl(server_fd_, F_SETFL, flags | O_NONBLOCK);
    }

    struct sockaddr_un addr = {};
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);

    if (connect(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        LOG_E("Failed to connect to server on socket: %s, error: %s", socket_path_.c_str(), strerror(errno));
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }
    LOG_I("Connected to server on socket: %s", socket_path_.c_str());
    return true;
}

// 自动重连3次，如果失败，则抛出异常
inline void SharedBufferSubscriber::connect_to_server() {
    for (int i = 0; i < 3; ++i) {
        if (try_connect_to_server()) {
            return;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    throw std::runtime_error("Failed to connect to server");
}

inline void SharedBufferSubscriber::submit_recv() {
    if (!connected_.load()) return;

    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);

    auto* data = new OperationData(OperationData::RECV, new Message());

    io_uring_prep_recvmsg(sqe, server_fd_, &data->msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferSubscriber::handle_recv(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_received = cqe->res;
    auto* msg = data->msg;
    // 继续接收
    submit_recv();

    // LOG_I("%llu us bytes received: %d, msg_control: %p, msg_controllen: %zu", get_current_us(), bytes_received, msg->hdr.msg_control, msg->hdr.msg_controllen);
    if (bytes_received <= 0) {
        // 连接丢失或错误
        connected_.store(false);
        delete data->msg;
        delete data;
        return;
    }

    int shared_fd = -1;
    for (struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr); cmsg; cmsg = CMSG_NXTHDR(&msg->hdr, cmsg)) {
        if (cmsg->cmsg_level == SOL_SOCKET &&
            cmsg->cmsg_type  == SCM_RIGHTS &&
            cmsg->cmsg_len   >= CMSG_LEN(sizeof(int))) {
            shared_fd = *reinterpret_cast<int*>(CMSG_DATA(cmsg));
            break;
        }
    }

    // 从控制消息中提取fd和TransportData
    TransportData & transport_data = msg->transport_data;
    void* mapped_addr = nullptr;
    if (shared_fd > 0) {
        LOG_I("%llu us Received buffer, buffer id: %d, fd: %d", get_current_us(), transport_data.buffer_id, shared_fd);
        // 检查是否已经映射此缓冲区（持久映射避免昂贵的mmap/munmap）
        {
            std::lock_guard<std::mutex> map_lock(mapped_buffers_mutex_);
            auto it = mapped_buffers_.find(transport_data.buffer_id);
            if (it != mapped_buffers_.end()) {
                // 缓冲区已映射，重用现有映射
                mapped_addr = it->second.addr;
                // close the next opend fd
                close(shared_fd);
            } else {
                // 首次看到此缓冲区，创建持久映射
                mapped_addr = mmap(nullptr, transport_data.meta.data_size,
                                    PROT_READ, MAP_SHARED, shared_fd, 0);

                if (mapped_addr == MAP_FAILED) {
                    close(shared_fd);
                    LOG_E("Failed to mmap buffer");
                } else {
                    // 存储持久映射
                    mapped_buffers_[transport_data.buffer_id] = 
                        MappedBufferInfo(mapped_addr, transport_data.meta.data_size, shared_fd);
                    LOG_I("Mapped buffer id: %d, size: %d, fd: %d", transport_data.buffer_id, transport_data.meta.data_size, shared_fd);
                }
            }
        }
    } else {
        // LOG_I("Received frame, buffer id: %d, frame id: %ld, delay: %ld ms", transport_data.buffer_id, transport_data.meta.frame_id, (get_current_us() - transport_data.meta.timestamp)/1000);
        // 检查是否已经映射此缓冲区（持久映射避免昂贵的mmap/munmap）
        {
            std::lock_guard<std::mutex> map_lock(mapped_buffers_mutex_);
            auto it = mapped_buffers_.find(transport_data.buffer_id);
            if (it != mapped_buffers_.end())
            {
                mapped_addr = it->second.addr;
                shared_fd = it->second.fd;
            }
        }
        if (mapped_addr) {
            // 创建同步缓冲区并添加到队列
            if (buffer_callback_) {
                SubscriberData sub_data;
                sub_data.meta = transport_data.meta;  // 复制TransportMetadata
                sub_data.data_ptr = mapped_addr;  // 复制数据指针
                sub_data.buffer_id = transport_data.buffer_id;  // 复制缓冲区ID
                sub_data.fd = shared_fd;  // 复制缓冲区ID
                sub_data.transport_type = config_.type;  // 设置传输类型
                // LOG_I("Notice frame arrived, buffer id: %d, frame id: %ld, delay: %ld ms", sub_data.buffer_id, sub_data.meta.frame_id, (get_current_us() - sub_data.meta.timestamp)/1000);
                buffer_callback_(sub_data);
            } else {
                // 使用双缓冲机制 - 写入写缓冲区 - 交换读写缓冲区，并通知消费者有新数据可用。
                double_buffer_.set_write_buffer(SyncBuffer(msg->transport_data, mapped_addr, shared_fd));
            }
            update_stats_received(msg->transport_data.meta.data_size, true);
        } else {
            LOG_E("Failed to mmap buffer");
            update_stats_received(0, false);
        }        
    }
    
    delete data->msg;
    delete data;
}

inline void SharedBufferSubscriber::submit_release(uint64_t buffer_id) {
    if (!connected_.load()) return;

    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) {
        LOG_E("Failed to get sqe for release message");
        return;
    }

    auto* msg = new Message();
    msg->transport_data.buffer_id = buffer_id;
    auto* data = new OperationData(OperationData::SEND, msg);
    io_uring_prep_sendmsg(sqe, server_fd_, &msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferSubscriber::handle_send(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_sent = cqe->res;
    delete data->msg;
    delete data;

    if (bytes_sent < 0) {
        // 发送失败，连接可能丢失
        connected_.store(false);
    }
    // 对于释放消息，不需要进一步操作
}

inline void SharedBufferSubscriber::update_stats_received(size_t bytes, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (success) {
        stats_.frames_received.fetch_add(1);
        stats_.bytes_received.fetch_add(bytes);
    } else {
        stats_.failed_operations.fetch_add(1);
    }
    stats_.last_update_time.store(std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

inline void SharedBufferSubscriber::process_events() {
    while (running_.load()) {
        struct io_uring_cqe* cqe;
        int ret = io_uring_wait_cqe(&ring_, &cqe);

        if (ret < 0) {
            if (errno == EINTR) continue;
            if (errno == EAGAIN) continue;
            // 其他错误，退出循环
            break;
        }

        auto* data = static_cast<OperationData*>(io_uring_cqe_get_data(cqe));
        if (!data) {
            io_uring_cqe_seen(&ring_, cqe);
            continue;
        }

        switch (data->type) {
            case OperationData::RECV:
                handle_recv(cqe, data);
                break;
            case OperationData::SEND:
                handle_send(cqe, data);
                break;
        }

        io_uring_cqe_seen(&ring_, cqe);
    }
}

} // namespace video_transport

#endif // SHARED_BUFFER_SUBSCRIBER_H