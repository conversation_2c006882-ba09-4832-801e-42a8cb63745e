#!/bin/bash

# RTSP播放卡住问题诊断脚本
# 用于分析数据源和传输层问题

echo "=== RTSP播放卡住问题诊断 ==="
echo "时间: $(date)"
echo ""

# 1. 检查系统资源
echo "1. 系统资源检查:"
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
echo "内存使用:"
free -h | grep Mem
echo "磁盘空间:"
df -h | grep -E "/$|/tmp"
echo ""

# 2. 检查相关进程
echo "2. 相关进程检查:"
echo "RTSP服务器进程:"
ps aux | grep -E "(rtsp_server|video)" | grep -v grep
echo ""
echo "视频相关进程:"
ps aux | grep -E "(v4l2|gstreamer|mpp)" | grep -v grep
echo ""

# 3. 检查网络连接
echo "3. 网络连接检查:"
echo "RTSP端口监听:"
netstat -tlnp | grep :8554
echo ""
echo "活跃的RTSP连接:"
netstat -tnp | grep :8554
echo ""

# 4. 检查视频设备
echo "4. 视频设备检查:"
echo "V4L2设备:"
ls -la /dev/video* 2>/dev/null || echo "没有找到V4L2设备"
echo ""
echo "DMA设备:"
ls -la /dev/dma* 2>/dev/null || echo "没有找到DMA设备"
echo ""

# 5. 检查共享内存和消息队列
echo "5. IPC资源检查:"
echo "共享内存段:"
ipcs -m | head -10
echo ""
echo "消息队列:"
ipcs -q | head -10
echo ""

# 6. 检查日志文件
echo "6. 日志文件检查:"
if [ -f "rtsp_picture_hang.log" ]; then
    echo "最新的RTSP日志 (最后20行):"
    tail -20 rtsp_picture_hang.log
    echo ""
    echo "appsrc推送统计:"
    grep "gst_app_src_push_internal" rtsp_picture_hang.log | wc -l
    echo "最后一次appsrc推送:"
    grep "gst_app_src_push_internal" rtsp_picture_hang.log | tail -1
    echo ""
else
    echo "未找到rtsp_picture_hang.log文件"
fi

# 7. 检查GStreamer环境
echo "7. GStreamer环境检查:"
echo "GStreamer版本:"
gst-launch-1.0 --version 2>/dev/null || echo "GStreamer未安装或不在PATH中"
echo ""
echo "可用的编码器:"
gst-inspect-1.0 | grep -E "(h264|h265|mpp)" | head -5
echo ""

# 8. 检查传输层配置
echo "8. 传输层配置检查:"
if [ -f "config/main_rtsp_server.json" ]; then
    echo "RTSP服务器配置:"
    cat config/main_rtsp_server.json | head -20
else
    echo "未找到RTSP服务器配置文件"
fi
echo ""

# 9. 实时监控建议
echo "9. 实时监控建议:"
echo "监控数据源状态:"
echo "  watch -n 1 'ps aux | grep video'"
echo ""
echo "监控网络连接:"
echo "  watch -n 1 'netstat -tnp | grep :8554'"
echo ""
echo "监控系统资源:"
echo "  htop"
echo ""

# 10. 问题排查步骤
echo "10. 问题排查步骤:"
echo "a) 检查数据源是否正常工作:"
echo "   - 确认视频采集进程是否运行"
echo "   - 检查数据发布者是否正常发送数据"
echo ""
echo "b) 检查传输层:"
echo "   - 验证DDS/共享内存/DMA传输是否正常"
echo "   - 检查缓冲区是否满或阻塞"
echo ""
echo "c) 检查RTSP服务器:"
echo "   - 查看详细日志输出"
echo "   - 监控appsrc数据推送频率"
echo ""
echo "d) 检查客户端:"
echo "   - 尝试不同的播放器"
echo "   - 检查网络连接质量"
echo ""

echo "=== 诊断完成 ==="
echo ""
echo "如果问题仍然存在，请："
echo "1. 运行修复后的RTSP服务器并收集新的日志"
echo "2. 检查数据源进程是否正常运行"
echo "3. 验证传输层配置是否正确"
echo "4. 考虑重启相关服务"
