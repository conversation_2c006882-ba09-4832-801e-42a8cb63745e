[BoTSORT]
enable_reid = false         ; if true, reid is enabled
enable_gmc = true           ; if true, Global Motion Compensation is enabled
track_high_thresh = 0.6     ; confidence threshold to classify a detection as high confidence detection. These detections are used in 1st level of association and to confirm a track
track_low_thresh = 0.1      ; lowest possible confidence to use a detection in the tracking algo. Any detection having confidence below this threshold is discarded
new_track_thresh = 0.7      ; confidence threshold to start a new track
track_buffer = 90           ; number governs the number of frames a track is kept alive without any detection. max_alive_age = frame_rate / 30.0 * track_buffer
match_thresh = 0.7          ; cost threshold to match a detection to a track (iou + embedding distance), only used in 1st level of association
proximity_thresh = 0.5      ; IoU distance (1 - IoU) threshold to reject a detection. If a detection <-> track box IoU distance is greater than this threshold, the match is rejected
appearance_thresh = 0.25    ; embedding distance threshold to reject a detection. If a detection <-> track embedding distance is greater than this threshold, the match is rejected
gmc_method = orb            ; possible values: orb, OptFlowModified, sparseOptFlow, OpenCV_VideoStab, ecc, THIS IS CASE SENSITIVE
frame_rate = 30             ; frame rate of the video being processed
lambda = 0.985              ; factor for fusing motion (mahalanobis distance) and appearance information; fused_distance = lambda * motion_distance + (1 - lambda) * appearance_distance