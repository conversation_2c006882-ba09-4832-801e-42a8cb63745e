#include "../include/transport/video_transport_interface.h"
#include "../include/transport/fastdds_video_transport.h"
#include "../include/transport/shared_buffer_publisher.h"
#include "../include/transport/shared_buffer_subscriber.h"
#include "../include/capture/v4l2_capture_interface.h"
#include "../include/capture/v4l2_utils.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <atomic>
#include <vector>

using namespace video_transport;
using namespace V4L2Capture;

static volatile bool running = true;
static std::atomic<int> frames_captured(0);
static std::atomic<int> frames_received(0);
static std::atomic<bool> mode_running(false);
static uint32_t run_time_sec = 30;

void signal_handler(int sig) {
    std::cout << "\nReceived signal " << sig << ", stopping..." << std::endl;
    running = false;
}

// Subscriber thread function
void subscriber_thread(TransportType type, const std::string& topic_name) {
    std::cout << "Starting subscriber thread for " << topic_name << " using ";
    switch (type) {
        case TransportType::FASTDDS:
            std::cout << "FastDDS";
            break;
        case TransportType::DMA:
            std::cout << "DMA";
            break;
        case TransportType::SHMEM:
            std::cout << "Shared Memory";
            break;
    }
    std::cout << std::endl;
    
    TransportConfig config;
    config.type = type;
    config.topic_name = topic_name;
    if (type == TransportType::FASTDDS) {
        config.domain_id = 0;
        config.max_samples = 5;
        config.timeout_ms = 1000;
    } else {
        config.buffer_size = V4L2FormatUtils::calculate_frame_size(V4L2_PIX_FMT_YUYV, 1920, 1080);
        config.ring_buffer_size = 3;
        config.timeout_ms = 1000;
    }
    
    auto subscriber = VideoTransportFactory::create_subscriber(config);
    if (!subscriber) {
        std::cerr << "Failed to create subscriber for " << topic_name << std::endl;
        return;
    }
    
    frames_received = 0;
    int frame_count = 0;
    
    while (mode_running) {
        IVideoSubscriber::SubscriberData data;
        BufferResult result = subscriber->receive_frame_buffer(data, 1000);
        if (result == BufferResult::SUCCESS) {
            frame_count++;
            frames_received++;
            // Print delay
            std::cout << "Subscriber (" << topic_name << "): Frame " << frame_count << " received, delay: " << (get_current_us() - data.meta.timestamp) / 1000 << " ms" << std::endl;
        } else if (result != BufferResult::TIMEOUT) {
            std::cerr << "Subscriber (" << topic_name << "): Error receiving frame: " << static_cast<int>(result) << std::endl;
        }
    }
    
    subscriber->cleanup();
    std::cout << "Subscriber (" << topic_name << ") finished. Total frames received: " << frame_count << std::endl;
}

// Publisher thread function for different transport types
void publisher_thread(TransportType type, const std::string& topic_name) {
    std::cout << "Starting publisher thread for " << topic_name << " using ";
    switch (type) {
        case TransportType::FASTDDS:
            std::cout << "FastDDS";
            break;
        case TransportType::DMA:
            std::cout << "DMA";
            break;
        case TransportType::SHMEM:
            std::cout << "Shared Memory";
            break;
    }
    std::cout << std::endl;
    
    TransportConfig config;
    config.type = type;
    config.topic_name = topic_name;
    if (type == TransportType::FASTDDS) {
        config.domain_id = 0;
        config.max_samples = 5;
        config.timeout_ms = 1000;
    } else {
        config.buffer_size = V4L2FormatUtils::calculate_frame_size(V4L2_PIX_FMT_YUYV, 1920, 1080);
        config.ring_buffer_size = 3;
        config.timeout_ms = 1000;
    }
    
    auto publisher = VideoTransportFactory::create_publisher(config);
    if (!publisher) {
        std::cerr << "Failed to create publisher for " << topic_name << std::endl;
        return;
    }
    
    // Create V4L2 device
    V4L2DeviceConfig v4l2_config;
    v4l2_config.device_path = "/dev/video0";
    v4l2_config.width = 1920;
    v4l2_config.height = 1080;
    v4l2_config.pixel_format = V4L2_PIX_FMT_YUYV;
    v4l2_config.fps = 30;
    v4l2_config.buffer_count = 3;
    v4l2_config.transport_type = type == TransportType::DMA ? "DMA" : (type == TransportType::SHMEM ? "SHMEM" : "FASTDDS");
    
    auto device = V4L2DeviceFactory::create_and_configure(v4l2_config, publisher.get());
    if (!device) {
        std::cerr << "Failed to create V4L2 device for " << topic_name << std::endl;
        return;
    }
    
    // Start streaming
    if (!device->start_streaming()) {
        std::cerr << "Failed to start streaming for " << topic_name << std::endl;
        return;
    }
    
    std::cout << "Publisher (" << topic_name << "): Started video capture" << std::endl;
    
    frames_captured = 0;
    int frame_count = 0;
    auto  last_capture_time = std::chrono::steady_clock::now();
    const int interval_ms = 1000 / v4l2_config.fps;
    
    while (mode_running) {
        V4L2Frame frame;
        if (device->capture_frame(frame, 1000)) {
            BufferResult result = publisher->publish_buffer(frame.publisher_data);
            if (result == BufferResult::SUCCESS) {
                frame_count++;
                frames_captured++;
                auto now = std::chrono::steady_clock::now();
                auto capture_interval = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_capture_time).count();

                if (capture_interval < interval_ms) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(interval_ms - capture_interval));
                }
                last_capture_time = now;

            } else if (result != BufferResult::TIMEOUT) {
                std::cerr << "Publisher (" << topic_name << "): Failed to publish frame: " << static_cast<int>(result) << std::endl;
            }
            
            device->release_frame(frame);
        }
    }
    
    // Cleanup
    device->stop_streaming();
    publisher->cleanup();
    
    std::cout << "Publisher (" << topic_name << ") finished. Total frames captured: " << frame_count << std::endl;
}

// Function to run a single transport mode for 10 seconds
void run_transport_mode(TransportType type, const std::string& topic_name) {
    std::cout << "\n=== Running " << topic_name << " mode for " << run_time_sec << " seconds ===" << std::endl;
    
    // Start mode running flag
    mode_running = true;
    
    // Create publisher and subscriber threads
    std::thread pub_thread(publisher_thread, type, topic_name);
    std::thread sub_thread(subscriber_thread, type, topic_name);
    
    std::this_thread::sleep_for(std::chrono::seconds(run_time_sec));
    
    // Stop the mode
    mode_running = false;
    
    // Wait for threads to complete
    if (pub_thread.joinable()) {
        pub_thread.join();
    }
    if (sub_thread.joinable()) {
        sub_thread.join();
    }
    
    // Print statistics
    std::cout << "\n=== Statistics for " << topic_name << " ===" << std::endl;
    std::cout << "Frames captured: " << frames_captured.load() << std::endl;
    std::cout << "Frames received: " << frames_received.load() << std::endl;
    std::cout << "Capture rate: " << frames_captured.load() / (double)run_time_sec << " FPS" << std::endl;
    std::cout << "Receive rate: " << frames_received.load() / (double)run_time_sec << " FPS" << std::endl;
}

int main(int argc, char* argv[]) {
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    if (argc == 1) {
        run_time_sec = 30;
    } else if (argc >= 2) {
        run_time_sec = atoi(argv[1]);
    }

    run_time_sec = atoi(argv[1]);
    
    std::cout << "=== Video Capture Test with Sequential Transport Modes ===" << std::endl;
    
    // Run each transport mode
    run_transport_mode(TransportType::FASTDDS, "fastdds_test_frames");
    run_transport_mode(TransportType::DMA, "dma_test_frames");
    run_transport_mode(TransportType::SHMEM, "shmem_test_frames");
    
    std::cout << "\n=== All transport modes completed ===" << std::endl;
    
    return 0;
}