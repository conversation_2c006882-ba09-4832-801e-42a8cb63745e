#!/bin/sh
#
# Start video tracking for main camera. (rk3576)
#
#

MAIN_CAM_CAPTURE_PID=/var/run/main_cam_cap
MAIN_CAM_TRACKIGN_PID=/var/run/main_cam_tracking
MAIN_CAM_RTSP_PID=/var/run/main_cam_rtsp 

MAIN_CAPTURE_RUN="while true;do v4l2_capture_main; sleep 2; done"
MAIN_TRACKING_RUN="while true;do rknn_track_main; sleep 2; done"
MAIN_RTSP_RUN="while true;do rtsp_server_main; sleep 2; done"

[ -f "/usr/bin/v4l2_capture_main" ] || { echo "v4l2_capture_main not exist!"; exit 1; }
[ -f "/usr/bin/rtsp_server_main" ] || { echo "rtsp_server_main not exist!"; exit 1; }
[ -f "/usr/bin/rknn_track_main" ] || { echo "rtsp_server_main not exist!"; exit 1; }

start_main_cam()
{
  start-stop-daemon -b -S -q -m -p $MAIN_CAM_CAPTURE_PID --startas /bin/sh -- -c "${MAIN_CAPTURE_RUN}"
  sleep 1
  start-stop-daemon -b -S -q -m -p $MAIN_CAM_TRACKIGN_PID --startas /bin/sh -- -c "${MAIN_TRACKING_RUN}"
  sleep 1
  start-stop-daemon -b -S -q -m -p $MAIN_CAM_RTSP_PID --startas /bin/sh -- -c "${MAIN_RTSP_RUN}"
}


stop_main_cam()
{
  start-stop-daemon -K -q -p $MAIN_CAM_RTSP_PID
  start-stop-daemon -K -q -p $MAIN_CAM_TRACKIGN_PID
  start-stop-daemon -K -q -p $MAIN_CAM_CAPTURE_PID
  killall v4l2_capture_main
  killall rtsp_server_main
  killall rknn_track_main
}


case "$1" in
  start)
    start_main_cam
    ;;
  stop)
    stop_main_cam
    ;;
  restart|reload)
    stop_main_cam
    start_main_cam
    ;;
  *)
    echo "Usage: $0 {start|stop|restart}"
    exit 1
esac

