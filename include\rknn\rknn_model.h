#ifndef RKNN_MODEL_H
#define RKNN_MODEL_H

#include <rknn_api.h>
#include <vector>
#include <string>
#include <iostream>
#include <cstdio>
#include <cstring>
#include <execution> // 需要 C++17 支持
#include <omp.h> // OpenMP 头文件
#include <chrono>
#include <iomanip> // for std::setw and std::setprecision
#include "../common.h"

using namespace std::chrono;

static int ctx_counter = 0;
// Input and output data types
template <typename T>
class rknn_model
{

public:
    rknn_model()
    {
        ctx_index = ctx_counter++;
    }
    ~rknn_model()
    {
        LOG_I("Destroy model, ctx_index = %d", ctx_index);
#if defined(ENABLE_ZERO_COPY)
        release_mems();
#endif

        if (rknn_inputs != nullptr)
        {
            for (uint32_t i = 0; i < io_num.n_input; ++i)
            {
                free(rknn_inputs[i].buf);
            }
            free(rknn_inputs);
            rknn_inputs = nullptr;
        }

        if (rknn_outputs != nullptr)
        {
            for (uint32_t i = 0; i < io_num.n_output; ++i)
            {
                free(rknn_outputs[i].buf);
            }
            free(rknn_outputs);
            rknn_outputs = nullptr;
        }
        release_model();
    }

    int init_model(const std::string &model_path)
    {
        FILE *fp = fopen(model_path.c_str(), "rb"); // 打开模型文件
        if (fp == nullptr)
        {
            LOG_E("Failed to open model file: %s", model_path.c_str());
            return -1;
        }
        fseek(fp, 0, SEEK_END);        // 将文件指针移动到文件末尾，以便获取文件大小
        size_t model_size = ftell(fp); // 获取当前文件指针的位置，即文件大小
        fseek(fp, 0, SEEK_SET);        // 将文件指针重新移动到文件开头，以便从头开始读取文件内容

        double model_size_mb = static_cast<double>(model_size) / (1024 * 1024);
        LOG_I("Model size: %f MB", model_size_mb); // 打印模型参数大小（以MB为单位）

        unsigned char *model_data = new unsigned char[model_size]; // 分配一块内存，大小为文件大小，用于存储模型数据
        if (model_data == nullptr)
        {
            LOG_E("Failed to allocate memory for model data.");
            fclose(fp);
            return -1;
        }

        // 读取模型数据
        size_t items_read = fread(model_data, 1, model_size, fp);
        if (items_read != model_size)
        {
            LOG_E("Failed to read the entire model file.");
            delete[] model_data;
            fclose(fp);
            return -1;
        }
        fclose(fp);

        // 初始化rknn上下文，并设置多个标识
        // int ret = rknn_init(&ctx, model_data, model_size,
        //                     // RKNN_FLAG_MEM_ALLOC_OUTSIDE | // 用于表示模型输入、输出、权重、中间tensor内存全部由用户分配
        //                     RKNN_FLAG_COLLECT_PERF_MASK | // 用于运行时查询网络各层时间
        //                                                   // RKNN_FLAG_EXECUTE_FALLBACK_PRIOR_DEVICE_GPU |  // 表示所有NPU不支持的层优先选择运行在GPU上
        //                         RKNN_FLAG_ENABLE_SRAM,    // 表示中间tensor内存尽可能分配在SRAM上
        //                     NULL);

        int ret = rknn_init(&ctx, model_data, model_size,
                            // RKNN_FLAG_MEM_ALLOC_OUTSIDE | // 用于表示模型输入、输出、权重、中间tensor内存全部由用户分配
                            // RKNN_FLAG_COLLECT_PERF_MASK | // 用于运行时查询网络各层时间
                            // RKNN_FLAG_EXECUTE_FALLBACK_PRIOR_DEVICE_GPU |  // 表示所有NPU不支持的层优先选择运行在GPU上
                            RKNN_FLAG_ENABLE_SRAM, // 表示中间tensor内存尽可能分配在SRAM上
                            NULL);

        if (ret < 0)
        {
            LOG_E("rknn_init error ret=%d", ret);
            delete[] model_data;
            return -1;
        }

        rknn_core_mask core_mask = static_cast<rknn_core_mask>(1 << ctx_index); // 设置核心掩码
        ret = rknn_set_core_mask(ctx, core_mask);
        if (ret < 0)
        {
            LOG_E("Failed to set core mask for ctx. ret=%d", ret);
            delete[] model_data;
            return -1;
        }

        delete[] model_data;
        query_model_info();
        init_rknn_io();
        LOG_I("Init model success, ctx_index = %d", ctx_index);
        return 0;
    }

    int init_model(rknn_context &ctx_in)
    {
        int ret = rknn_dup_context(&ctx_in, &ctx);
        if (ret < 0)
        {
            LOG_E("Failed to duplicate context to ctx%d. ret=%d", ctx_index, ret);
            return -1;
        }

        rknn_core_mask core_mask = static_cast<rknn_core_mask>(1 << ctx_index); // 设置核心掩码
        ret = rknn_set_core_mask(ctx, core_mask);
        if (ret < 0)
        {
            LOG_E("Failed to set core mask for ctx. ret=%d", ret);
            return -1;
        }

        query_model_info();
        init_rknn_io();
        LOG_I("Init model success, ctx_index = %d", ctx_index);
        return 0;
    }

    // 打印量化和反量化信息
    void print_quantization_info() const
    {
        std::cout << "Quantization and Dequantization Information:" << std::endl;

        // 打印输入张量的量化和反量化信息
        std::cout << "Input Tensors:" << std::endl;
        for (const auto &attr : input_attrs)
        {
            std::cout << "  Name: " << attr.name << std::endl;
            if (attr.qnt_type == RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC)
            {
                std::cout << "    Quantization Type: Affine Asymmetric" << std::endl;
                std::cout << "    Quantization Formula: Q = (R / scale) + zero_point" << std::endl;
                std::cout << "    Dequantization Formula: R = (Q - zero_point) * scale" << std::endl;
                std::cout << "    Scale: " << attr.scale << std::endl;
                std::cout << "    Zero Point: " << attr.zp << std::endl; // 使用 zp 而不是 fl
            }
            else if (attr.qnt_type == RKNN_TENSOR_QNT_DFP)
            {
                std::cout << "    Quantization Type: Dynamic Fixed Point" << std::endl;
                std::cout << "    Quantization Formula: Q = R * (2^(-fl))" << std::endl;
                std::cout << "    Dequantization Formula: R = Q * (2^(fl))" << std::endl;
                std::cout << "    Fraction Length (fl): " << static_cast<int>(attr.fl) << std::endl;
            }
            else
            {
                std::cout << "    Quantization Type: None" << std::endl;
            }
        }

        // 打印输出张量的量化和反量化信息
        std::cout << "Output Tensors:" << std::endl;
        for (const auto &attr : output_attrs)
        {
            std::cout << "  Name: " << attr.name << std::endl;
            if (attr.qnt_type == RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC)
            {
                std::cout << "    Quantization Type: Affine Asymmetric" << std::endl;
                std::cout << "    Quantization Formula: Q = (R / scale) + zero_point" << std::endl;
                std::cout << "    Dequantization Formula: R = (Q - zero_point) * scale" << std::endl;
                std::cout << "    Scale: " << attr.scale << std::endl;
                std::cout << "    Zero Point: " << attr.zp << std::endl;
            }
            else if (attr.qnt_type == RKNN_TENSOR_QNT_DFP)
            {
                std::cout << "    Quantization Type: Dynamic Fixed Point" << std::endl;
                std::cout << "    Quantization Formula: Q = R * (2^(-fl))" << std::endl;
                std::cout << "    Dequantization Formula: R = Q * (2^(fl))" << std::endl;
                std::cout << "    Fraction Length (fl): " << static_cast<int>(attr.fl) << std::endl;
            }
            else
            {
                std::cout << "    Quantization Type: None" << std::endl;
            }
        }
        return;
    }

    // 封装推理过程
    int run_infer(cv::Mat &input, T &output)
    {
        auto start_infer = high_resolution_clock::now();
        int ret = pre_process(input);
        if (ret != 0)
        {
            LOG_E("Pre-processing failed!");
            return -1;
        }
        auto pre_process_end = high_resolution_clock::now();
        ret = rknn_run(ctx, NULL);
        if (ret != 0)
        {
            LOG_E("rknn_run fail! ret=%d", ret);
            return -1;
        }
        auto infer_end = high_resolution_clock::now();
        ret = post_process(output);
        if (ret != 0)
        {
            LOG_E("Post-processing failed!");
            return -1;
        }
        auto post_process_end = high_resolution_clock::now();
        duration<double, std::milli> pre_process_time = pre_process_end - start_infer;
        duration<double, std::milli> infer_time = infer_end - pre_process_end;
        duration<double, std::milli> post_process_time = post_process_end - infer_end;
        LOG_I("[Inference] Pre-processing: %.3f ms, Inference: %.3f ms, Post-processing: %.3f ms", pre_process_time.count(), infer_time.count(), post_process_time.count());
        return 0;
    }

    // 获取输入输出格式
    const std::vector<rknn_tensor_attr> &get_input_attrs() const { return input_attrs; }
    const std::vector<rknn_tensor_attr> &get_output_attrs() const { return output_attrs; }
    rknn_context &get_context() { return ctx; }
    int get_ctx_index() { return ctx_index; }
    int get_model_width() { return model_width; }
    int get_model_height() { return model_height; }
    const rknn_input_output_num &get_io_num() { return io_num; }
    rknn_input *get_input_ptr() { return rknn_inputs; }
    rknn_output *get_output_ptr() { return rknn_outputs; }

#if defined(ENABLE_ZERO_COPY)
    rknn_tensor_mem *get_input_mem_ptr(int mem_index) { return input_mems[mem_index]; }
    const std::vector<rknn_tensor_attr> &get_input_native_attrs() const { return input_native_attrs; }
    const std::vector<rknn_tensor_attr> &get_output_native_attrs() const { return output_native_attrs; }
    const std::vector<rknn_tensor_mem *> &get_output_mems() const { return output_mems; }
#endif
    // pre_process input data
    virtual int pre_process(cv::Mat &input) = 0;
    // post_process output data
    virtual int post_process(T &output) = 0;

private:
    rknn_context ctx;
    int ctx_index;
    std::vector<rknn_tensor_attr> input_attrs;
    std::vector<rknn_tensor_attr> output_attrs;
    rknn_input_output_num io_num;
    rknn_input *rknn_inputs;
    rknn_output *rknn_outputs;

    int model_height;
    int model_width;
    int model_channel;

#if defined(ENABLE_ZERO_COPY)
    std::vector<rknn_tensor_attr> output_native_attrs;
    std::vector<rknn_tensor_attr> input_native_attrs;
    std::vector<rknn_tensor_mem *> input_mems;  // 输入内存
    std::vector<rknn_tensor_mem *> output_mems; // 输出内存

    void initialize_mems()
    {

        input_mems.resize(io_num.n_input);
        output_mems.resize(io_num.n_output);
        // std::cerr << "Input size:" << input_attrs[0].size_with_stride << std::endl;
        for (size_t j = 0; j < io_num.n_input; ++j)
        {
            // default input type is int8 (normalize and quantize need compute in outside)
            // if set uint8, will fuse normalize and quantize to npu
            // input_attrs[j].pass_through = 0;
            input_native_attrs[j].type = RKNN_TENSOR_UINT8;
            // input_attrs[j].fmt = RKNN_TENSOR_NHWC;

            input_mems[j] = rknn_create_mem(ctx, input_native_attrs[j].size_with_stride);
            if (!input_mems[j])
            {
                throw std::runtime_error("rknn_create_mem failed for input");
            }
            // 设置输入内存
            int ret = rknn_set_io_mem(ctx, input_mems[j], &input_native_attrs[j]);
            if (ret < 0)
            {
                printf("input_mems rknn_set_io_mem fail! ret=%d\n", ret);
            }
        }
        // std::cerr << "OutPut size:" << output_attrs[0].size_with_stride << std::endl;
        // std::cerr << "OutPut n_elems:" << output_attrs[0].n_elems << std::endl;
        for (size_t j = 0; j < io_num.n_output; ++j)
        {
            // output_attrs[j].type = RKNN_TENSOR_FLOAT32; //RKNN_TENSOR_INT8 输出改成int8，需要手动反量化
            // output_attrs[j].fmt = RKNN_TENSOR_NC1HWC2;
            output_mems[j] = rknn_create_mem(ctx, output_native_attrs[j].size_with_stride); //.n_elems); //*sizeof(float)
            if (!output_mems[j])
            {
                throw std::runtime_error("rknn_create_mem failed for output");
            }
            // 设置输出内存
            rknn_set_io_mem(ctx, output_mems[j], &output_native_attrs[j]);
        }
        // RKNN_TENSOR_UINT8;
    }

    void release_mems()
    {
        // 释放输入输出内存
        for (size_t j = 0; j < io_num.n_input; ++j)
        {
            if (input_mems[j] != nullptr && input_mems[j]->virt_addr != nullptr)
            { // 检查指针和虚拟地址是否为nullptr
                rknn_destroy_mem(ctx, input_mems[j]);
                // delete input_mems[j]; // 如果是动态分配的，请确保这里使用正确的释放方式
                input_mems[j] = nullptr; // 确保释放后将指针设为nullptr
            }
        }

        // 遍历所有输出内存
        for (size_t j = 0; j < io_num.n_output; ++j)
        {
            if (output_mems[j] != nullptr && output_mems[j]->virt_addr != nullptr)
            { // 检查指针和虚拟地址是否为nullptr
                rknn_destroy_mem(ctx, output_mems[j]);
                // delete output_mems[j]; // 如果是动态分配的，请确保这里使用正确的释放方式
                output_mems[j] = nullptr; // 确保释放后将指针设为nullptr
            }
        }
    }
#endif

    void dump_tensor_attr(rknn_tensor_attr *attr)
    {
        char dims[128] = {0};
        for (uint32_t i = 0; i < attr->n_dims; ++i)
        {
            size_t idx = strlen(dims);
            sprintf(&dims[idx], "%d%s", attr->dims[i], (i == attr->n_dims - 1) ? "" : ", ");
        }
        LOG_I("  index=%d, name=%s, n_dims=%d, dims=[%s], n_elems=%d, size=%d, w_stride = %d, size_with_stride = %d, "
                  "fmt=%s, type=%s, qnt_type=%s, "
                  "zp=%d, scale=%f",
                  attr->index, attr->name, attr->n_dims, dims, attr->n_elems, attr->size, attr->w_stride, attr->size_with_stride,
                  get_format_string(attr->fmt), get_type_string(attr->type), get_qnt_type_string(attr->qnt_type), attr->zp,
                  attr->scale);
    }

    void query_model_info()
    {
        // Query SDK version
        rknn_sdk_version sdk_version;
        int ret = rknn_query(ctx, RKNN_QUERY_SDK_VERSION, &sdk_version, sizeof(sdk_version));
        if (ret < 0)
        {
            LOG_E("rknn_query RKNN_QUERY_SDK_VERSION error ret=%d", ret);
            return;
        }
        LOG_I("SDK API Version: %s", sdk_version.api_version);
        LOG_I("Driver Version: %s", sdk_version.drv_version);

        // Query custom string
        rknn_custom_string custom_string;
        ret = rknn_query(ctx, RKNN_QUERY_CUSTOM_STRING, &custom_string, sizeof(custom_string));
        if (ret < 0)
        {
            LOG_E("rknn_query RKNN_QUERY_CUSTOM_STRING error ret=%d", ret);
            return;
        }
        LOG_I("custom string:");
        LOG_I("%s", custom_string.string);

        // 3. 查询模型输入、输出 Tensor 的个数、维度、形状、名称、数据类型、量化类型并打印
        // rknn_input_output_num io_num;
        ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
        if (ret < 0)
        {
            LOG_E("rknn_query RKNN_QUERY_IN_OUT_NUM error ret=%d", ret);
            return;
        }
        LOG_I("Model input num: %d, output num: %d", io_num.n_input, io_num.n_output); // 1

        // 查询常规输入 Tensor 的属性
        input_attrs.resize(io_num.n_input);
        for (uint32_t i = 0; i < io_num.n_input; ++i)
        {
            input_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &input_attrs[i], sizeof(rknn_tensor_attr));
            if (ret != RKNN_SUCC)
            {
                LOG_E("rknn_query RKNN_QUERY_INPUT_ATTR error ret=%d", ret);
                return;
            }
            LOG_I("Input Tensor %d info:", i);
            dump_tensor_attr(&(input_attrs[i]));
        }

#if defined(ENABLE_ZERO_COPY)
        // 查询硬件最优的输入Tensor属性
        input_native_attrs.resize(io_num.n_input);
        for (uint32_t i = 0; i < io_num.n_input; ++i)
        {
            input_native_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_NATIVE_INPUT_ATTR, &input_native_attrs[i], sizeof(rknn_tensor_attr));
            if (ret != RKNN_SUCC)
            {
                LOG_E("rknn_query RKNN_QUERY_NATIVE_INPUT_ATTR ret=%d", ret);
                return;
            }
            LOG_I("Input Tensor %d info:", i);
            dump_tensor_attr(&(input_native_attrs[i]));
        }
#endif

        // 查询常规输出 Tensor 的属性
        output_attrs.resize(io_num.n_output);
        for (uint32_t i = 0; i < io_num.n_output; ++i)
        {
            output_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &output_attrs[i], sizeof(rknn_tensor_attr)); // RKNN_QUERY_NATIVE_OUTPUT_ATTR
            if (ret != RKNN_SUCC)
            {
                LOG_E("rknn_query RKNN_QUERY_OUTPUT_ATTR ret=%d", ret);
                return;
            }
            LOG_I("Output Tensor %d info:", i);
            dump_tensor_attr(&(output_attrs[i]));
        }

#if defined(ENABLE_ZERO_COPY)
        // 查询硬件最优的输出Tensor属性
        output_native_attrs.resize(io_num.n_output);
        for (uint32_t i = 0; i < io_num.n_output; ++i)
        {
            output_native_attrs[i].index = i;
            ret = rknn_query(ctx, RKNN_QUERY_NATIVE_OUTPUT_ATTR, &output_native_attrs[i], sizeof(rknn_tensor_attr));
            if (ret < 0)
            {
                LOG_E("rknn_query RKNN_QUERY_NATIVE_OUTPUT_ATTR ret=%d", ret);
                return;
            }
            LOG_I("Output Tensor %d info:", i);
            dump_tensor_attr(&(output_native_attrs[i]));
        }
#endif

        if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
        {
            LOG_I("model is NCHW input fmt");
            model_channel = input_attrs[0].dims[1];
            model_height = input_attrs[0].dims[2];
            model_width = input_attrs[0].dims[3];
        }
        else
        {
            LOG_I("model is NHWC input fmt");
            model_height = input_attrs[0].dims[1];
            model_width = input_attrs[0].dims[2];
            model_channel = input_attrs[0].dims[3];
        }
        LOG_I("model input height=%d, width=%d, channel=%d",
                  model_height, model_width, model_channel);
    }

    void init_rknn_io()
    {
#if defined(ENABLE_ZERO_COPY)
        initialize_mems();
#endif

        rknn_inputs = (rknn_input *)malloc(sizeof(rknn_input) * io_num.n_input);
        memset(rknn_inputs, 0, sizeof(rknn_input) * io_num.n_input);
        for (uint32_t i = 0; i < io_num.n_input; ++i)
        {
            rknn_inputs[i].index = i;
            rknn_inputs[i].type = RKNN_TENSOR_UINT8;
            rknn_inputs[i].size = model_width * model_height * model_channel;
            rknn_inputs[i].buf = malloc(rknn_inputs[i].size); // 预分配内存
            rknn_inputs[i].fmt = RKNN_TENSOR_NHWC;
            rknn_inputs[i].pass_through = 0;
        }

        rknn_outputs = (rknn_output *)malloc(sizeof(rknn_output) * io_num.n_output);
        for (uint32_t i = 0; i < io_num.n_output; ++i)
        {
            memset(&rknn_outputs[i], 0, sizeof(rknn_output));
            rknn_outputs[i].is_prealloc = 1;
            if (RKNN_TENSOR_FLOAT16 == output_attrs[i].type || RKNN_TENSOR_BFLOAT16 == output_attrs[i].type || RKNN_TENSOR_FLOAT32 == output_attrs[i].type)
            {
                rknn_outputs[i].want_float = 1;
            } else {
                rknn_outputs[i].want_float = 0;
            }
            rknn_outputs[i].index = i;
#if defined(ENABLE_ZERO_COPY)
            rknn_outputs[i].size = output_native_attrs[i].size_with_stride;
#else
            rknn_outputs[i].size = output_attrs[i].size_with_stride;
#endif
            rknn_outputs[i].buf = malloc(rknn_outputs[i].size); // 预分配内存
        }
        LOG_I("rknn_io init success, ctx_index = %d, input ptr = %p, output ptr = %p", ctx_index, rknn_inputs, rknn_outputs);
    }

    void release_model()
    {
        int ret = rknn_destroy(ctx);
        if (ret < 0)
        {
            LOG_E("rknn_destroy error ret=%d", ret);
        }
    }
};

#endif // RKNN_MODEL_H
