#pragma once

#include <memory>
#include <string>
#include <vector>
#include <opencv2/opencv.hpp>
#include "yolo_utils.h"
#include "../common.h"

namespace tracking
{
    struct TrackObj
    {
        cv::Rect box;
        int track_id;
        char* label;
        float score;
    };

    class BaseTracker
    {
    protected:
        std::vector<char *> &class_names;

    public:
        explicit BaseTracker(std::vector<char *> &class_names) : class_names(class_names) {} // 构造函数。
        virtual ~BaseTracker() = default; // 析构函数。
        virtual void update(cv::Mat &frame, object_detect_result_list &results, std::vector<TrackObj> &track_obj) = 0; // 虚函数。
    };
}// namespace tracking

