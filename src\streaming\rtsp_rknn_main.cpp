#include "../include/common.h"
#include "../include/config/rtsp_rknn_config.h"
#include "../include/transport/video_transport_interface.h"
#include "../include/transport/fastdds_video_transport.h"
#include "../include/transport/shared_buffer_publisher.h"
#include "../include/rknn/yolo11_track.h"
#include "../include/streaming/rtsp_client.h"
#include "../include/hardware/rga_accelerator.h"
#include "../include/hardware/mpp_decoder.h"
#include "../include/capture/v4l2_utils.h"
#include <opencv2/opencv.hpp>
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <fstream>
#include <chrono>
#include <thread>
#include <json/json.h>
#include <atomic>
#include <unordered_map>
#include <malloc.h>
#include <cstring>

// GStreamer headers
#include <gst/gst.h>
#include <gst/app/gstappsink.h>
#include <gst/video/video.h>

using namespace tracking;

// Define global variable class_names
std::vector<char *> class_names;

// GStreamer pipeline structure
struct GStreamerPipeline {
    GstElement *pipeline;
    GstElement *appsink;
    GMainLoop *loop;
    std::thread loop_thread;
    std::atomic<bool> running{false};

    GStreamerPipeline() : pipeline(nullptr), appsink(nullptr), loop(nullptr) {}
    ~GStreamerPipeline() { cleanup(); }

    void cleanup() {
        running = false;
        if (loop && g_main_loop_is_running(loop)) {
            g_main_loop_quit(loop);
        }
        if (loop_thread.joinable()) {
            loop_thread.join();
        }
        if (pipeline) {
            gst_element_set_state(pipeline, GST_STATE_NULL);
            gst_object_unref(pipeline);
            pipeline = nullptr;
        }
        if (loop) {
            g_main_loop_unref(loop);
            loop = nullptr;
        }
    }
};

// 全局变量
static std::unique_ptr<yolo11_track> g_yolo11_track;
static std::unique_ptr<video_transport::IVideoPublisher> g_publisher;
static std::unique_ptr<RTSPClient> g_rtsp_client;
static std::unique_ptr<MPPDecoder> g_decoder;
static std::unique_ptr<GStreamerPipeline> g_gst_pipeline;
static std::atomic<bool> g_shutdown_requested{false};
static RTSPRKNNConfig g_config; // 全局配置变量
static std::unordered_map<void *, video_transport::IVideoPublisher::PublisherData> g_publisher_buffers;

void signal_handler(int signal) {
    LOG_I("Received signal %d, shutting down...", signal);
    g_shutdown_requested.store(true);
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "  -c, --config FILE          Configuration file\n"
              << "  -u, --rtsp-url URL         RTSP stream URL\n"
              << "  -o, --output-topic TOPIC   Output topic (default: main_rknn_tracked_frames)\n"
              << "  --output-transport TYPE    Output transport type (default: DMA)\n"
              << "  -m, --model PATH           RKNN model file path\n"
              << "  --chip TYPE                Chip type (default: rk3576)\n"
              << "  --tcp                      Use TCP for RTSP (default: UDP)\n"
              << "  --decode-method METHOD     Decode method: mpp, gstreamer (default: mpp)\n"
              << "  --help                     Show help\n";
}

bool load_config_from_json(const std::string& config_file, RTSPRKNNConfig& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) return false;

    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;

    if (!Json::parseFromStream(builder, file, &root, &errors)) return false;

    // complete loading config parameters
    if (root.isMember("output_topic_name")) config.output_topic_name = root["output_topic_name"].asString();
    if (root.isMember("model_path")) config.model_path = root["model_path"].asString();
    if (root.isMember("chip")) config.chip = root["chip"].asString();
    if (root.isMember("box_threshold")) config.box_threshold = root["box_threshold"].asFloat();
    if (root.isMember("nms_threshold")) config.nms_threshold = root["nms_threshold"].asFloat();
    if (root.isMember("track_buffer")) config.track_buffer = root["track_buffer"].asInt();
    if (root.isMember("frame_rate")) config.frame_rate = root["frame_rate"].asInt();
    if (root.isMember("draw_boxes")) config.draw_boxes = root["draw_boxes"].asBool();
    if (root.isMember("draw_labels")) config.draw_labels = root["draw_labels"].asBool();
    if (root.isMember("draw_track_ids")) config.draw_track_ids = root["draw_track_ids"].asBool();
    if (root.isMember("debug_level")) config.debug_level = root["debug_level"].asInt();
    if (root.isMember("stats_interval")) config.stats_interval = root["stats_interval"].asInt();
    if (root.isMember("url")) config.url = root["url"].asString();
    if (root.isMember("rtsp_transport")) config.rtsp_transport = root["rtsp_transport"].asString();
    if (root.isMember("decode_method")) config.decode_method = root["decode_method"].asString();
    if (root.isMember("output_ring_buffer_size")) config.output_ring_buffer_size = root["output_ring_buffer_size"].asInt();
    if (root.isMember("output_transport_type")) config.output_transport_type = root["output_transport_type"].asString();
    if (root.isMember("output_width")) config.output_width = root["output_width"].asInt();
    if (root.isMember("output_height")) config.output_height = root["output_height"].asInt();
    if (root.isMember("output_format")) config.output_format = V4L2FormatUtils::string_to_pixelformat(root["output_format"].asString());
    if (root.isMember("fastdds_domain_id")) config.fastdds_domain_id = root["fastdds_domain_id"].asInt();
    if (root.isMember("fastdds_max_samples")) config.fastdds_max_samples = root["fastdds_max_samples"].asInt();

    return true;
}

void print_config(const RTSPRKNNConfig& config) {
    LOG_I("=== RTSP RKNN Track Configuration ===");
    LOG_I("RTSP URL: %s", config.url.c_str());
    LOG_I("RTSP Transport: %s", config.rtsp_transport.c_str());
    LOG_I("Decode Method: %s", config.decode_method.c_str());
    LOG_I("Output Topic: %s", config.output_topic_name.c_str());
    LOG_I("Model Path: %s", config.model_path.c_str());
    LOG_I("Chip: %s", config.chip.c_str());
    LOG_I("Box Threshold: %f", config.box_threshold);
    LOG_I("NMS Threshold: %f", config.nms_threshold);
    LOG_I("Track Buffer: %d", config.track_buffer);
    LOG_I("Frame Rate: %d", config.frame_rate);
    LOG_I("Draw Boxes: %s", config.draw_boxes ? "yes" : "no");
    LOG_I("Draw Labels: %s", config.draw_labels ? "yes" : "no");
    LOG_I("Draw Track IDs: %s", config.draw_track_ids ? "yes" : "no");
    LOG_I("Debug Level: %d", config.debug_level);
    LOG_I("Stats Interval: %d", config.stats_interval);
    LOG_I("Output Transport: %s", config.output_transport_type.c_str());
    LOG_I("Output Format: %s", V4L2FormatUtils::pixelformat_to_string(config.output_format).c_str());
    LOG_I("Output Width: %d", config.output_width);
    LOG_I("Output Height: %d", config.output_height);
    LOG_I("FastDDS Domain ID: %d", config.fastdds_domain_id);
    LOG_I("FastDDS Max Samples: %d", config.fastdds_max_samples);
    LOG_I("=====================================");
}

// 跟踪回调函数
void tracking_callback(cv::Mat& frame, std::vector<TrackObj>& track_objs) {    
    // 根据配置绘制跟踪结果
    // 使用全局配置
    extern RTSPRKNNConfig g_config;
    
    if (g_config.draw_boxes || g_config.draw_labels || g_config.draw_track_ids) {
        for (const auto& obj : track_objs) {
            // 绘制边界框
            if (g_config.draw_boxes) {
                cv::rectangle(frame,
                    cv::Point(obj.box.x, obj.box.y),
                    cv::Point(obj.box.x + obj.box.width, obj.box.y + obj.box.height),
                    cv::Scalar(0, 255, 0), 2);
            }
            
            // 绘制标签和跟踪ID
            if (g_config.draw_labels || g_config.draw_track_ids) {
                std::string label = "";
                if (g_config.draw_labels && obj.label) {
                    label += obj.label;
                }
                if (g_config.draw_track_ids) {
                    if (!label.empty()) label += " ";
                    label += "ID:" + std::to_string(obj.track_id);
                }
                
                if (!label.empty()) {
                    cv::Point text_pos(obj.box.x, obj.box.y - 5);
                    if (text_pos.y < 0) text_pos.y = obj.box.y + obj.box.height + 15;
                    
                    int baseLine = 0;
                    cv::Size label_size = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);
                    
                    // 绘制标签背景
                    cv::rectangle(frame,
                        cv::Point(text_pos.x, text_pos.y - label_size.height),
                        cv::Point(text_pos.x + label_size.width, text_pos.y + baseLine),
                        cv::Scalar(0, 0, 0), cv::FILLED);
                    
                    // 绘制标签文本
                    cv::putText(frame, label, text_pos, cv::FONT_HERSHEY_SIMPLEX,
                        0.5, cv::Scalar(255, 255, 255), 1);
                }
            }
        }
    }
    
    // 将处理后的帧发布到输出主题
    if (g_publisher) {
        // 从map中获取发布缓冲区数据结构
        auto it = g_publisher_buffers.find(frame.data);
        if (it == g_publisher_buffers.end()) {
            LOG_E("Failed to find publisher buffer for frame");
            return;
        }
        video_transport::IVideoPublisher::PublisherData pub_data = it->second;

        if (pub_data.data_ptr != frame.data) {
            LOG_E("Publisher buffer data pointer does not match frame data pointer");
            return;
        }
        // 设置元数据
        LOG_I("[Publisher] frame id: %ld, delay: %ld ms, track objs: %zu", pub_data.meta.frame_id, (get_current_us() - pub_data.meta.timestamp)/1000, track_objs.size());   
        pub_data.meta.width = frame.cols;
        pub_data.meta.height = frame.rows;
        pub_data.meta.format = g_config.output_format; // output format
        pub_data.meta.data_size = frame.total() * frame.elemSize();
        pub_data.meta.is_valid = true;
        pub_data.meta.is_keyframe = true;
        // 发布帧数据到输出主题
        g_publisher->publish_buffer(pub_data);
        // 释放发布缓冲区数据结构
        g_publisher_buffers.erase(it);
    }
}

// MPP解码NALU数据为YUV420_SP格式
bool mpp_decode_nalu(void* nalu_data, size_t nalu_size, int width, int height, int32_t codec_format, cv::Mat& frame) {
    if (!g_decoder) {
        // 第一次初始化解码器，根据编码格式创建解码器
        g_decoder = std::make_unique<MPPDecoder>(codec_format);
        if (!g_decoder->init(width, height, V4L2_PIX_FMT_NV12)) { // RK_MPP + rk3576 只支持解码为YUV
            LOG_E("[RTSP] Failed to initialize MPP decoder");
            return false;
        }
    }

    if (!g_decoder->is_initialized() || !g_decoder->decode_frame(nalu_data, nalu_size, [&](void* ptr, size_t size, int32_t width, int32_t height, int32_t hor_stride, int32_t ver_stride) {
        // 使用RGA将YUV420_SP转换为BGR格式
        if (!RGAAccelerator::color_convert(ptr, width, height, hor_stride, ver_stride, V4L2_PIX_FMT_NV12, frame.data, g_config.output_format)) {
            LOG_E("[RTSP] Format conversion failed");
            return;
        }
        LOG_D("[RTSP] MPP decode success, width: %d, height: %d, size: %zu", width, height, size);
    })) {
        LOG_E("[RTSP] MPP decode failed");
        return false;
    }
    return true;
}

// GStreamer 回调函数
static GstFlowReturn gst_new_sample_callback(GstAppSink *appsink, gpointer user_data) {
    GstSample *sample = gst_app_sink_pull_sample(appsink);
    if (!sample) {
        return GST_FLOW_ERROR;
    }

    GstBuffer *buffer = gst_sample_get_buffer(sample);
    GstCaps *caps = gst_sample_get_caps(sample);

    if (!buffer || !caps) {
        gst_sample_unref(sample);
        return GST_FLOW_ERROR;
    }

    // 获取视频信息
    GstVideoInfo video_info;
    if (!gst_video_info_from_caps(&video_info, caps)) {
        LOG_E("[GStreamer] Failed to get video info from caps");
        gst_sample_unref(sample);
        return GST_FLOW_ERROR;
    }

    // 映射缓冲区
    GstMapInfo map_info;
    if (!gst_buffer_map(buffer, &map_info, GST_MAP_READ)) {
        LOG_E("[GStreamer] Failed to map buffer");
        gst_sample_unref(sample);
        return GST_FLOW_ERROR;
    }

    // 处理帧数据
    try {
        video_transport::IVideoPublisher::PublisherData pub_data;
        if (g_publisher && g_publisher->acquire_buffer(pub_data) == video_transport::BufferResult::SUCCESS) {
            static uint64_t frame_counter = 0;
            pub_data.meta.is_valid = false;
            pub_data.meta.frame_id = ++frame_counter;
            pub_data.meta.timestamp = get_current_us();
            pub_data.meta.width = GST_VIDEO_INFO_WIDTH(&video_info);
            pub_data.meta.height = GST_VIDEO_INFO_HEIGHT(&video_info);
            pub_data.meta.format = g_config.output_format;

            // 创建 OpenCV Mat
            cv::Mat frame(pub_data.meta.height, pub_data.meta.width, CV_8UC3, pub_data.data_ptr);

            // 复制数据到输出缓冲区
            size_t expected_size = pub_data.meta.width * pub_data.meta.height * 3;
            if (map_info.size >= expected_size) {
                memcpy(pub_data.data_ptr, map_info.data, expected_size);
            } else {
                LOG_E("[GStreamer] Buffer size mismatch: expected %zu, got %zu", expected_size, map_info.size);
                g_publisher->publish_buffer(pub_data, false);
                gst_buffer_unmap(buffer, &map_info);
                gst_sample_unref(sample);
                return GST_FLOW_ERROR;
            }

            // 保存发布缓冲区数据结构到map中
            g_publisher_buffers[frame.data] = pub_data;

            // 推送到RKNN进行跟踪
            if (g_yolo11_track) {
                g_yolo11_track->push_frame(frame);
            }

            LOG_D("[GStreamer] Frame processed: %dx%d", pub_data.meta.width, pub_data.meta.height);
        }
    } catch (const std::exception& e) {
        LOG_E("[GStreamer] Exception in frame processing: %s", e.what());
    }

    gst_buffer_unmap(buffer, &map_info);
    gst_sample_unref(sample);
    return GST_FLOW_OK;
}

// 创建 GStreamer 管道
bool create_gstreamer_pipeline(const RTSPRKNNConfig& config) {
    // 初始化 GStreamer
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    g_gst_pipeline = std::make_unique<GStreamerPipeline>();

    // 构建管道字符串 - 使用 mppvideodec 解码器
    std::string pipeline_str = "rtspsrc location=" + config.url +
                              " protocols=" + (config.rtsp_transport == "tcp" ? "tcp" : "udp") +
                              " latency=0 ! " +
                              "rtph264depay ! " +
                              "h264parse config-interval=1 ! " +
                              "mppvideodec fast-mode=true ! " +
                              "videoconvert ! " +
                              "video/x-raw,format=BGR ! " +
                              "appsink name=sink sync=false max-buffers=1 drop=true";

    LOG_I("[GStreamer] Pipeline: %s", pipeline_str.c_str());

    // 创建管道
    GError *error = nullptr;
    g_gst_pipeline->pipeline = gst_parse_launch(pipeline_str.c_str(), &error);

    if (!g_gst_pipeline->pipeline || error) {
        LOG_E("[GStreamer] Failed to create pipeline: %s", error ? error->message : "Unknown error");
        if (error) g_error_free(error);
        return false;
    }

    // 获取 appsink 元素
    g_gst_pipeline->appsink = gst_bin_get_by_name(GST_BIN(g_gst_pipeline->pipeline), "sink");
    if (!g_gst_pipeline->appsink) {
        LOG_E("[GStreamer] Failed to get appsink element");
        return false;
    }

    // 设置 appsink 回调
    GstAppSinkCallbacks callbacks = {nullptr, nullptr, gst_new_sample_callback, nullptr};
    gst_app_sink_set_callbacks(GST_APP_SINK(g_gst_pipeline->appsink), &callbacks, nullptr, nullptr);

    return true;
}

// 启动 GStreamer 管道
bool start_gstreamer_pipeline() {
    if (!g_gst_pipeline || !g_gst_pipeline->pipeline) {
        LOG_E("[GStreamer] Pipeline not created");
        return false;
    }

    // 创建主循环
    g_gst_pipeline->loop = g_main_loop_new(nullptr, FALSE);

    // 启动管道
    GstStateChangeReturn ret = gst_element_set_state(g_gst_pipeline->pipeline, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        LOG_E("[GStreamer] Failed to start pipeline");
        return false;
    }

    // 启动主循环线程
    g_gst_pipeline->running = true;
    g_gst_pipeline->loop_thread = std::thread([&]() {
        LOG_I("[GStreamer] Main loop started");
        g_main_loop_run(g_gst_pipeline->loop);
        LOG_I("[GStreamer] Main loop stopped");
    });

    LOG_I("[GStreamer] Pipeline started successfully");
    return true;
}

int main(int argc, char* argv[]) {
    Logger::set_level(LEVEL_INFO);

    RTSPRKNNConfig config;
    std::string config_file;

    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"rtsp-url", required_argument, 0, 'u'},
        {"output-topic", required_argument, 0, 'o'},
        {"output-transport", required_argument, 0, 2},
        {"model", required_argument, 0, 'm'},
        {"chip", required_argument, 0, 1},
        {"tcp", no_argument, 0, 3},
        {"decode-method", required_argument, 0, 4},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };

    int option_index = 0;
    int c;

    // 加载默认配置
    load_config_from_json("/opt/video_service/config/rtsp_rknn_track.json", config);

    // 第一次解析：只获取配置文件路径
    optind = 1;
    while ((c = getopt_long(argc, argv, "c:u:o:m:h", long_options, &option_index)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件
    if (!config_file.empty()) {
        if (!load_config_from_json(config_file, config)) {
            LOG_E("Failed to load configuration file");
            return 1;
        }
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1;
    option_index = 0;
    while ((c = getopt_long(argc, argv, "c:u:o:m:h", long_options, &option_index)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 'u':
                config.url = optarg;
                break;
            case 'o':
                config.output_topic_name = optarg;
                break;
            case 'm':
                config.model_path = optarg;
                break;
            case 1: // --chip
                config.chip = optarg;
                break;
            case 2: // --output-transport
                config.output_transport_type = optarg;
                break;
            case 3: // --tcp
                config.rtsp_transport = "tcp";
                break;
            case 4: // --decode-method
                config.decode_method = optarg;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            case '?':
                print_usage(argv[0]);
                return 1;
            default:
                break;
        }
    }

    // 检查必需参数
    if (config.url.empty()) {
        LOG_E("RTSP URL is required");
        print_usage(argv[0]);
        return 1;
    }

    // 打印配置
    print_config(config);

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 根据解码方法选择初始化方案
    if (config.decode_method == "gstreamer") {
        LOG_I("Using GStreamer decode method");
        // GStreamer 方案不需要单独的 RTSP 客户端
    } else {
        LOG_I("Using MPP decode method");
        // 创建RTSP客户端
        g_rtsp_client = std::make_unique<RTSPClient>();
        if (!g_rtsp_client->init(config.url, config.rtsp_transport == "tcp")) {
            LOG_E("Failed to initialize RTSP client");
            return 1;
        }
        LOG_I("RTSP client connected: %dx%d", g_rtsp_client->get_width(), g_rtsp_client->get_height());
    }

    // 创建输出发布者
    video_transport::TransportConfig publisher_config;
    publisher_config.topic_name = config.output_topic_name;
    if (config.output_transport_type == "SHMEM") {
        publisher_config.type = video_transport::TransportType::SHMEM;
    } else if (config.output_transport_type == "FASTDDS") {
        publisher_config.type = video_transport::TransportType::FASTDDS;
    } else {
        publisher_config.type = video_transport::TransportType::DMA;
    }

    if (publisher_config.type == video_transport::TransportType::FASTDDS) {
        publisher_config.domain_id = config.fastdds_domain_id;
        publisher_config.max_samples = config.fastdds_max_samples;
        publisher_config.timeout_ms = 1000;
    } else {
        publisher_config.buffer_size = V4L2FormatUtils::calculate_frame_size(config.output_format, config.output_width, config.output_height);
        publisher_config.ring_buffer_size = config.output_ring_buffer_size;
        publisher_config.timeout_ms = 1000;
    }

    LOG_I("Creating publisher for %s using %s transport",
          config.output_topic_name.c_str(), config.output_transport_type.c_str());

    g_publisher = video_transport::VideoTransportFactory::create_publisher(publisher_config);
    if (!g_publisher) {
        LOG_E("Failed to create publisher for %s", config.output_topic_name.c_str());
        return 1;
    }

    if (!g_publisher->initialize()) {
        LOG_E("Failed to initialize publisher");
        return 1;
    }

    // 保存配置到全局变量
    g_config = config;

    // 创建RKNN跟踪器
    g_yolo11_track = std::make_unique<yolo11_track>(
        config.chip,
        config.model_path,
        config.frame_rate,
        config.track_buffer,
        config.box_threshold,
        config.nms_threshold,
        tracking_callback
    );

    if (!g_yolo11_track) {
        LOG_E("Failed to create yolo11_track");
        return 1;
    }

    LOG_I("RTSP RKNN tracking service is running. Press Ctrl+C to stop");

    // 根据解码方法选择处理流程
    if (config.decode_method == "gstreamer") {
        // GStreamer 方案
        if (!create_gstreamer_pipeline(config)) {
            LOG_E("Failed to create GStreamer pipeline");
            return 1;
        }

        if (!start_gstreamer_pipeline()) {
            LOG_E("Failed to start GStreamer pipeline");
            return 1;
        }

        // GStreamer 主循环 - 等待关闭信号
        while (!g_shutdown_requested.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    } else {
        // MPP 方案 - 原有的主循环
        uint64_t frame_id = 0;
        while (!g_shutdown_requested.load()) {
            video_transport::FrameMetadata frame_meta;
            void * nalu_buffer = nullptr;
            uint64_t start_ts = get_current_us();

            // 从RTSP客户端获取NALU数据
            nalu_buffer = g_rtsp_client->get_frame(frame_meta);

            if (!nalu_buffer) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                continue;
            }

            LOG_D("[RTSP] Get frame success, frame id: %ld, size: %zu, width: %d, height: %d, format: %d",
                frame_meta.frame_id, frame_meta.data_size, frame_meta.width, frame_meta.height, frame_meta.format);

            video_transport::IVideoPublisher::PublisherData pub_data; // 发布缓冲区数据结构
            uint64_t rx_ts = get_current_us();

            try {
                if (g_publisher->acquire_buffer(pub_data) == video_transport::BufferResult::SUCCESS) {
                    uint64_t acquire_ts = get_current_us();
                    pub_data.meta.is_valid = false;
                    pub_data.meta.frame_id = frame_meta.frame_id;
                    pub_data.meta.timestamp = frame_meta.timestamp; // 记录接收时间戳，用于计算延迟时间

                    // 将接收到的帧转换为OpenCV Mat, 使用发布缓冲区的内存
                    cv::Mat frame(config.output_height, config.output_width, CV_8UC3, pub_data.data_ptr);

                    // 使用MPP解码NALU数据
                    if (!mpp_decode_nalu(nalu_buffer, frame_meta.data_size, frame_meta.width, frame_meta.height, frame_meta.format, frame)) {
                        LOG_E("[RTSP] Failed to decode NALU frame");
                        g_publisher->publish_buffer(pub_data, false); // 发布失败，将发布缓冲区数据结构还回池中
                        continue; // 继续下一帧
                    } else {
                        LOG_D("[RTSP] Decode NALU frame success");
                        // 保存发布缓冲区数据结构到map中
                        g_publisher_buffers[frame.data] = pub_data;
                        uint64_t convert_ts = get_current_us();

                        // 推送到RKNN进行跟踪
                        g_yolo11_track->push_frame(frame);
                        LOG_I("[RTSP] Frame id: %ld, cost: %ld ms, rx: %ld ms, convert: %ld ms",
                            pub_data.meta.frame_id, (get_current_us() - start_ts) / 1000,
                            (rx_ts - start_ts) / 1000, (convert_ts - acquire_ts) / 1000);
                    }
                }
            } catch (const std::exception& e) {
                LOG_E("[RTSP] acquire_buffer Exception: %s", e.what());
            } // try
        } // while (!g_shutdown_requested.load())
    }

    LOG_I("RTSP RKNN tracking service is stopping...");

    // 清理
    g_yolo11_track->stop(); // 停止跟踪器
    g_yolo11_track.reset(); // 销毁跟踪器

    if (g_decoder) { // 销毁解码器
        g_decoder->cleanup();
    }

    if (g_rtsp_client) { // 清理RTSP客户端
        g_rtsp_client->cleanup();
    }

    if (g_gst_pipeline) { // 清理GStreamer管道
        g_gst_pipeline->cleanup();
    }

    if (g_publisher) {
        g_publisher->cleanup();
    }

    LOG_I("RTSP RKNN tracking service shutdown complete");
    return 0;
}
