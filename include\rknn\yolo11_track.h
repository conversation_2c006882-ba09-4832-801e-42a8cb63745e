#pragma once
#include "../common.h"
#include "yolo11_model.h"
#include "infer_threadpool.h"
#include "base_tracker.h"
#include <fstream> // 包含文件流头文件。
#include <filesystem> // 包含文件系统头文件。
#include <cstring> // 包含字符串头文件。
#include <opencv2/opencv.hpp>
#include <thread>

namespace fs = std::filesystem;

extern std::vector<char *> class_names;
using namespace tracking;

#ifdef USE_BYTETRACK
#include "BYTETracker.h" 
class ByteTracker : public BaseTracker
{
private:
    BYTETracker tracker;
public:
    ByteTracker(std::vector<char *> & class_names, int frame_rate = 30, int track_buffer = 60) : BaseTracker(class_names), tracker(frame_rate, track_buffer) {}
    ~ByteTracker() = default;
    void update(cv::Mat & frame, object_detect_result_list &results, std::vector<TrackObj> &track_obj)
    {
        std::vector<Object> objects;
        for (int i = 0; i < results.count; i++)
        {
            object_detect_result &det = results.results[i];
            objects.emplace_back(Object{
                {
                    static_cast<float>(det.box.left),
                    static_cast<float>(det.box.top),
                    static_cast<float>(det.box.right - det.box.left),
                    static_cast<float>(det.box.bottom - det.box.top)
                },
                det.cls_id,
                det.prop, frame}); // 复制图像数据
        }

        vector<STrack> output_stracks = tracker.update(objects);
        track_obj.clear();
        for (auto & track : output_stracks)
        {
            track_obj.emplace_back(TrackObj{
                {
                    static_cast<int>(track.tlwh[0]),
                    static_cast<int>(track.tlwh[1]),
                    static_cast<int>(track.tlwh[2]),
                    static_cast<int>(track.tlwh[3]),
                },
                track.track_id, class_names[track.class_id], track.score});
        }
    }
};
#endif

#ifdef USE_BOTSORT
#include "BoTSORT.h" // 包含 BoTSORT 头文件。

class BotSortTracker : public BaseTracker
{
private:
    std::unique_ptr<BoTSORT> tracker;
public:
    BotSortTracker(std::vector<char *> & class_names, std::string tracker_base_path) : BaseTracker(class_names) {
        std::string tracker_config_path = tracker_base_path + "/tracker.ini"; // 跟踪器配置文件路径。
        std::string gmc_config_path = tracker_base_path + "/gmc.ini"; // 全局运动补偿配置文件路径。
        std::string reid_config_path = tracker_base_path + "/reid.ini"; // 重识别配置文件路径。
        std::string reid_onnx_model_path = tracker_base_path + "/reid.onnx"; // 重识别模型文件路径。
        tracker = std::make_unique<BoTSORT>(tracker_config_path, gmc_config_path, reid_config_path, reid_onnx_model_path);        
    }

    ~BotSortTracker() = default; // 析构函数。
    void update(cv::Mat & frame, object_detect_result_list &results, std::vector<TrackObj> &track_obj) {
        std::vector<Detection> detections;
        for (int i = 0; i < results.count; i++)
        {
            object_detect_result &det = results.results[i];
            detections.emplace_back(Detection{
                {
                    static_cast<float>(det.box.left),
                    static_cast<float>(det.box.top),
                    static_cast<float>(det.box.right - det.box.left),
                    static_cast<float>(det.box.bottom - det.box.top)
                },
                det.cls_id,
                det.prop});
        }
        std::vector<std::shared_ptr<Track>> tracks = tracker->track(detections, frame);
        track_obj.clear();
        for (auto & track : tracks)
        {
            std::vector<float> bbox_tlwh = track->get_tlwh(); // 获取目标框的左上角和宽高信息。
            track_obj.emplace_back(TrackObj{
                {
                    static_cast<int>(bbox_tlwh[0]),
                    static_cast<int>(bbox_tlwh[1]),
                    static_cast<int>(bbox_tlwh[2]),
                    static_cast<int>(bbox_tlwh[3])
                }, // 目标框的左上角和宽高信息。
                track->track_id, class_names[track->get_class_id()], track->get_score()});
        }
    }
};
#endif

class yolo11_track
{
private:
    std::atomic<bool> stop_flag; // 停止标志。
    rw_bufferpool<rknn_general_output<object_detect_result_list>> rknn_output_bufferpool;
    std::unique_ptr<infer_threadpool<object_detect_result_list>> infer_runner;
    std::unique_ptr<BaseTracker> tracker; // 跟踪器指针。
    thread track_thread;

    void track_runnable(void (*cb)(cv::Mat &, std::vector<TrackObj> &))
    {
        std::vector<TrackObj> track_obj;
        while (!stop_flag)
        {
            auto *output_buffer = rknn_output_bufferpool.get_ready_buffer();
            auto start_track = std::chrono::system_clock::now();
#ifdef NO_TRACKING
            object_detect_result_list &results = output_buffer->data.results;
            track_obj.clear();
            for (int i = 0; i < results.count; i++)
            {
                object_detect_result &det = results.results[i];
                track_obj.emplace_back(TrackObj{
                    {
                        static_cast<float>(det.box.left),
                        static_cast<float>(det.box.top),
                        static_cast<float>(det.box.right - det.box.left),
                        static_cast<float>(det.box.bottom - det.box.top)
                    }, // 目标框的左上角和宽高信息。
                    det.cls_id, class_names[det.cls_id], det.prop});
            }            
#else
            tracker->update(output_buffer->data.image, output_buffer->data.results, track_obj);
#endif
            auto end_track = std::chrono::system_clock::now();
            std::chrono::duration<double, std::milli> track_time = end_track - start_track;
            LOG_I("[Tracker] %.3f ms", track_time.count());
            cb(output_buffer->data.image, track_obj);              // 调用回调函数，将跟踪结果传递给上层应用。
            rknn_output_bufferpool.set_buffer_idle(output_buffer); // 设置缓冲区为空闲状态
        }
    }

public:
    static std::unique_ptr<rknn_model<object_detect_result_list>> create_model(const float box_threshold, const float nms_threshold) { return std::make_unique<yolo11_model>(box_threshold, nms_threshold); }

    yolo11_track(
        const string &chip,
        const string &model_path,
        const int frame_rate,
        const int track_buffer,
        const float box_threshold,
        const float nms_threshold,
        void (*track_callback)(cv::Mat &, std::vector<TrackObj> &)) :
                stop_flag(false),
                rknn_output_bufferpool(5),
                infer_runner(std::make_unique<infer_threadpool<object_detect_result_list>>(chip, model_path, &rknn_output_bufferpool, create_model, box_threshold, nms_threshold))
    {

        const std::string &config_base_path = fs::path(model_path).parent_path().string();
        const std::string &labels_path = (fs::path(config_base_path) / "labels.txt").string();

        if (!fs::exists(model_path)) // 如果模型文件不存在，则返回。
        {
            LOG_E("Model file not found: %s", model_path.c_str());
            return;
        }
        LOG_I("Model file found: %s", model_path.c_str());

        if (fs::exists(labels_path))
        {
            std::ifstream file(labels_path);
            if (file.is_open())
            {
                std::string line;
                while (std::getline(file, line))
                {
                    class_names.push_back(strdup(line.c_str())); // 复制字符串并将其添加到向量中。
                }
                file.close();
                LOG_I("Loaded %d class names from %s", class_names.size(), labels_path.c_str());
            }
        }
        else
        {
            LOG_E("Failed to open labels.txt file: %s", labels_path.c_str());
            return;
        }

#ifdef USE_BYTETRACK
        LOG_I("Using ByteTracker");
        tracker = std::make_unique<ByteTracker>(class_names, frame_rate, track_buffer);
#endif // USE_BYTETRACK

#ifdef USE_BOTSORT
        LOG_I("Using BoTSORT tracker");
        LOG_I("Config base path: %s", config_base_path.c_str());
        (void) frame_rate; // 避免未使用变量警告
        (void) track_buffer; // 避免未使用变量警告
        tracker = std::make_unique<BotSortTracker>(class_names, config_base_path); // 创建 BotSortTracker 对象。
#endif // USE_BOTSORT

        if (!tracker)
        {
            LOG_E("Failed to create tracker");
            return;
        }
        LOG_I("Tracker created successfully");
        track_thread = std::thread(&yolo11_track::track_runnable, this, track_callback); // 启动跟踪线程。
    }

    bool push_frame(cv::Mat & frame) {
        if (!infer_runner) {
            LOG_E("Infer runner not initialized");
            return false;
        }
        if (stop_flag.load()) {
            LOG_E("Infer runner is stopped");
            return false;
        }        
        return infer_runner->push_capture_frame(frame); // 调用推理线程池的推送帧函数。
    }

    void stop() {
        stop_flag.store(true);
        if (infer_runner) {
            infer_runner->stop();
        }
        if (track_thread.joinable()) {
            track_thread.join();
        }
        LOG_I("RKNN tracking service is stopped");
    }
};