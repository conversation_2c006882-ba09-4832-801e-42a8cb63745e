#ifndef SIMPLIFIED_BUFFER_MANAGER_H
#define SIMPLIFIED_BUFFER_MANAGER_H

#include <fcntl.h>
#include <unistd.h>
#include <sys/mman.h>
#include <linux/memfd.h>
#include <linux/dma-buf.h>
#include <linux/dma-heap.h>
#include <atomic>
#include <vector>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <stdexcept>
#include <chrono>
#include <algorithm>
#include "../common.h"

// Forward declaration for memfd_create
extern "C" int memfd_create(const char *name, unsigned int flags);

namespace video_transport {

// Simplified buffer management - let kernel handle fd lifecycle
class SharedBufferManager {
public:

    // Buffer types
    enum class BufferType {
        DMA,    // Hardware DMA buffer
        SHMEM   // Shared memory buffer
    };

    struct BufferSlot {
        int fd;                         // File descriptor (kernel manages lifecycle)
        void* addr;                     // Mapped address
        size_t size;                    // Buffer size
        uint64_t buffer_id;             // Business logic ID
        BufferType type;                // Buffer type
        std::atomic<bool> ready;        // Simple ready flag for synchronization
        uint64_t last_state_change_us;  // 最后一次状态变化的时间戳
        
        BufferSlot() : fd(-1), addr(nullptr), size(0), buffer_id(0), 
                        type(BufferType::SHMEM), ready(false), last_state_change_us(0) {}
    };

    SharedBufferManager(BufferType type, size_t buffer_size, size_t pool_size)
        : buffer_type_(type), buffer_size_(buffer_size) {
        // Pre-allocate buffer pool
        for (size_t i = 0; i < pool_size; ++i) {
            auto buffer = std::make_unique<BufferSlot>();
            buffer->buffer_id = i;
            buffer->size = buffer_size;
            buffer->type = type;
            
            buffer_pool_.push_back(std::move(buffer));
            free_buffers_.push(buffer_pool_.back().get());
        }
    }

    ~SharedBufferManager() {
        cleanup();
    }

    // Acquire a free buffer for production with write protection
    BufferSlot* acquire_buffer() {
        std::unique_lock<std::mutex> lock(pool_mutex_);
        
        // Wait for available buffer
        pool_cv_.wait(lock, [this]() { return !free_buffers_.empty(); });
        
        auto* buffer = free_buffers_.front();
        free_buffers_.pop();
        
        // Lazy allocation - only allocate when actually needed
        if (buffer->fd == -1) {
            if (!allocate_buffer(buffer)) {
                // Allocation failed, return to pool
                free_buffers_.push(buffer);
                LOG_E("Failed to allocate buffer");
                return nullptr;
            }
        }
        
        // Set up write protection: only producer can write
        buffer->ready.store(false);
        buffer->last_state_change_us = get_current_us();
        
        // Re-map with write permissions for producer
        set_memory_protection(buffer, true, false);
        return buffer;
    }

    // Publish buffer as ready (producer done) with read-only protection
    void publish_buffer(BufferSlot* buffer) {
        if (!buffer) return;

        // Change memory protection to read-only for consumers
        set_memory_protection(buffer, false, true);
        
        // Mark as ready for consumers
        buffer->ready.store(true);
        buffer->last_state_change_us = get_current_us();

        // Return to free pool
        std::lock_guard<std::mutex> lock(pool_mutex_);
        free_buffers_.push(buffer);
        pool_cv_.notify_one();
    }

    // Get buffer by ID (for remote release)
    BufferSlot* find_buffer(uint64_t buffer_id) {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        if (buffer_id >= buffer_pool_.size()) {
            return nullptr;
        }
        return buffer_pool_[buffer_id].get();
    }

    void cleanup() {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        
        for (auto& buffer : buffer_pool_) {
            if (buffer->addr && buffer->addr != MAP_FAILED) {
                munmap(buffer->addr, buffer->size);
            }
            if (buffer->fd >= 0) {
                close(buffer->fd);
            }
        }
        
        buffer_pool_.clear();
        // Clear queue
        std::queue<BufferSlot*> empty;
        free_buffers_.swap(empty);
    }

    // Statistics
    struct Stats {
        size_t total_buffers;
        size_t free_buffers;
        size_t allocated_buffers;
    };

    Stats get_stats() const {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        Stats stats;
        stats.total_buffers = buffer_pool_.size();
        stats.free_buffers = free_buffers_.size();
        stats.allocated_buffers = stats.total_buffers - stats.free_buffers;
        return stats;
    }

private:
    bool allocate_buffer(BufferSlot* buffer) {
        if (buffer_type_ == BufferType::DMA) {
            buffer->fd = allocate_dma_buffer(buffer_size_);
        } else {
            buffer->fd = allocate_shmem_buffer(buffer_size_);
        }
        
        if (buffer->fd < 0) {
            return false;
        } else {
            int prot = PROT_READ | PROT_WRITE;
            void * address = mmap(nullptr, buffer_size_, prot, MAP_SHARED, buffer->fd, 0);
            if (address == MAP_FAILED) {
                LOG_E("Failed to mmap buffer: %s", strerror(errno));
                close(buffer->fd);
                return false;
            }
            buffer->addr = address;
            return true;
        }
    }

    int allocate_dma_buffer(size_t size) {
        // 定义变量，用于存储打开的DMA heap设备文件描述符。
        int dma_heap_fd = -1;
        
        // 定义DMA heap设备路径数组。注意，数组的最后一个元素为nullptr，表示数组的结束。
        const char* dma_heap_paths[] = {
            "/dev/dma_heap/system-uncached",
            "/dev/dma_heap/system",
            "/dev/dma_heap/reserved",
            nullptr
        };

        // 循环尝试打开DMA heap设备。
        for (int i = 0; dma_heap_paths[i] != nullptr; i++) {
            dma_heap_fd = open(dma_heap_paths[i], O_RDWR);
            if (dma_heap_fd >= 0) {
                LOG_I("Opened DMA heap: %s", dma_heap_paths[i]);  // 日志记录打开的DMA heap设备路径。
                break;  // 找到第一个可用的DMA heap设备，退出循环
            } else {
                LOG_I("Failed to open %s: %s", dma_heap_paths[i], strerror(errno));  // 日志记录打开失败的原因。
            }
        }

        if (dma_heap_fd >= 0) {
            struct dma_heap_allocation_data alloc_data;
            alloc_data.len        = size;
            alloc_data.fd_flags   = O_RDWR | O_CLOEXEC;
            alloc_data.heap_flags = 0;
            alloc_data.fd         = 0;

            if (ioctl(dma_heap_fd, DMA_HEAP_IOCTL_ALLOC, &alloc_data) == 0 && alloc_data.fd > 0) {
                LOG_I("Allocated real DMA buffer fd=%d, alloc_fd=%d, size=%zu", dma_heap_fd, alloc_data.fd, size);
                return alloc_data.fd;
            } else {
                LOG_E("DMA heap allocation failed: %s", strerror(errno));
                close(dma_heap_fd);  // 关闭打开的DMA heap设备文件描述符，以便下次循环尝试下一个设备。
                return -1;
            }
        } else {
            LOG_W("Failed to open DMA heap: %s", strerror(errno));  // 日志记录失败原因，但不返回错误码。
            return -1;
        }
    }

    int allocate_shmem_buffer(size_t size) {
        if (size == 0) {
            LOG_E("Invalid size: 0");
            return -1;
        }

        int fd = memfd_create("video_buffer", MFD_CLOEXEC | MFD_ALLOW_SEALING);
        if (fd < 0) {
            LOG_E("Failed to create memfd: %s", strerror(errno));
            return -1;
        }

        if (ftruncate(fd, size) < 0) {
            LOG_E("Failed to truncate buffer: %s", strerror(errno));
            close(fd);
            return -1;
        }

        // 注意：F_SEAL_WRITE 会让映射区域不可写，先别加，调通后再加
        // fcntl(fd, F_ADD_SEALS, F_SEAL_SHRINK | F_SEAL_GROW | F_SEAL_WRITE);
        LOG_I("Allocated SHMEM buffer fd: %d, size: %zu", fd, size);
        return fd;
    }

    // Set memory protection for buffer (read-only or read-write)
    bool set_memory_protection(BufferSlot* buffer, bool writable, bool readable) {
        if (!buffer) {
            return false;
        }

        if (buffer->type == BufferType::SHMEM) {
            // 共享内存，不需要设置内存保护，直接返回true。
            return true;
        } else if (buffer->type == BufferType::DMA) {
            struct dma_buf_sync sync = {0};
            if (writable) sync.flags |= DMA_BUF_SYNC_END | DMA_BUF_SYNC_WRITE;
            if (readable) sync.flags |= DMA_BUF_SYNC_START | DMA_BUF_SYNC_READ;
            
            // Note: DMA_BUF_SYNC does not have offset and len fields in all kernel versions
            // We'll skip setting them to avoid compilation errors
            
            if (ioctl(buffer->fd, DMA_BUF_IOCTL_SYNC, &sync) < 0) {
                return false;
            }
            return true;
        }
        return false;
    }

    const BufferType buffer_type_;
    const size_t buffer_size_;
    
    std::vector<std::unique_ptr<BufferSlot>> buffer_pool_;
    std::queue<BufferSlot*> free_buffers_;
    mutable std::mutex pool_mutex_;
    std::condition_variable pool_cv_;

    uint64_t get_current_us() const {
        auto now = std::chrono::steady_clock::now().time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(now).count();
    }
};

} // namespace video_transport

#endif // SIMPLIFIED_BUFFER_MANAGER_H
