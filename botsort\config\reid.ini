[ReID]
enable_TF32 = true                                      ; if ReID is enabled, set this to enable TF32 inference
enable_FP16 = true                                      ; if re-id is enabled (i.e. model_path is not commented out), set this to true if you want to use fp16 inference
input_layer_name = images                               ; layer name of the input layer in the ONNX model
output_layer_names = [output]                           ; layer of of the output layer in the ONNX model
batch_size = 1                                          ; batch size for model inference
input_layer_dimensions = [1, 3, 256, 128]               ; input layer dimensions for the model
output_layer_dimensions = [1, 512]                      ; output layer dimensions for the model
distance_metric = euclidean                             ; distance metric for calculating feature distances
swapRB = true                                           ; swap red and blue channels in the input image (i.e. from default BGR to RGB)
trt_log_level = 4                                       ; [0=CRITICAL, 1=ERROR, 2=WARNING, 3=INFO, 4=VERBOSE]


