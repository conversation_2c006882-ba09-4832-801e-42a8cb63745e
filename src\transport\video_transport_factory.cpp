#include "../include/transport/video_transport_interface.h"
#include "../include/transport/fastdds_video_transport.h"
#include "../include/transport/shared_buffer_publisher.h"
#include "../include/transport/shared_buffer_subscriber.h"
#include <stdexcept>

namespace video_transport {

std::unique_ptr<IVideoPublisher> VideoTransportFactory::create_publisher(const TransportConfig& config) {
    std::unique_ptr<IVideoPublisher> publisher;
    switch (config.type) {
        case TransportType::FASTDDS:
            publisher = std::make_unique<FastDDSVideoPublisher>(config);
            break;
        
        case TransportType::DMA:
        case TransportType::SHMEM:
            // Use simplified transport for better performance and reduced complexity
            publisher = std::make_unique<SharedBufferPublisher>(config);
            break;
        
        default:
            throw std::invalid_argument("Unsupported transport type for publisher");
    }
    if (!publisher->initialize()) {
        throw std::runtime_error("Failed to initialize publisher with provided config");
    }
    
    return publisher;
}

std::unique_ptr<IVideoSubscriber> VideoTransportFactory::create_subscriber(const TransportConfig& config) {
    std::unique_ptr<IVideoSubscriber> subscriber;
    switch (config.type) {
        case TransportType::FASTDDS:
            subscriber = std::make_unique<FastDDSVideoSubscriber>(config);
            break;
        
        case TransportType::DMA:
        case TransportType::SHMEM:
            // Use simplified transport for better performance and reduced complexity
            subscriber = std::make_unique<SharedBufferSubscriber>(config);
            break;
        
        default:
            throw std::invalid_argument("Unsupported transport type for subscriber");
    }
    if (!subscriber->initialize()) {
        throw std::runtime_error("Failed to initialize subscriber with provided config");
    }
    
    return subscriber;
}

} // namespace video_transport