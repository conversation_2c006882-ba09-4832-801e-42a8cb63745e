#pragma once

#include <atomic> // 包含 atomic 头文件，用于定义原子类型和原子操作。
#include <iostream>
#include <thread>
#include <vector>
#include <atomic>
#include <functional> // 包含函数头文件。用于定义函数对象和函数指针。
#include <memory> // 包含 memory 头文件，用于定义智能指针。
#include <opencv2/opencv.hpp>
#include "rw_bufferpool.h"
#include "rknn_model.h"
#include "../common.h"

using namespace std;

template <typename T>
struct rknn_general_output
{
    cv::Mat image; // 图像数据
    T results;     // 检测结果
};

template <typename T>
class infer_threadpool
{
private:
    std::atomic<bool> stop_flag;
    vector<thread> infer_thread_vector;
    vector<std::unique_ptr<rknn_model<T>>> models; // 模型对象vector。

    rw_bufferpool<cv::Mat> capture_bufferpool;
    rw_bufferpool<rknn_general_output<T>> * output_bufferpool; // 注意：这里使用了指针

    void infer_runnable(rknn_model<T> *model)
    {
        while (!stop_flag)
        {
            auto fetch_buffer_start = std::chrono::high_resolution_clock::now();

            auto *output_buffer = output_bufferpool->get_idle_buffer();
            if (output_buffer == nullptr)
            {
                LOG_E("Null output buffer!");
                std::this_thread::sleep_for(std::chrono::milliseconds(5));
                continue;
            }

            // 添加空指针检查
            auto *capture_buffer = capture_bufferpool.get_ready_buffer();
            if (capture_buffer == nullptr)
            {
                LOG_E("Null capture buffer!");
                output_bufferpool->set_buffer_idle(output_buffer);
                std::this_thread::sleep_for(std::chrono::milliseconds(5));
                continue;
            }

            auto infer_start = std::chrono::high_resolution_clock::now();
            try
            {
                if (model->run_infer(capture_buffer->data, output_buffer->data.results) != 0)
                {
                    LOG_E("Inference failed!");
                    output_bufferpool->set_buffer_idle(output_buffer);
                    capture_bufferpool.set_buffer_idle(capture_buffer);
                    continue;
                }
            }
            catch (const std::exception &e)
            {
                LOG_E("Exception in inference: %s", e.what());
                output_bufferpool->set_buffer_idle(output_buffer);
                capture_bufferpool.set_buffer_idle(capture_buffer);
                continue;
            }
            output_buffer->data.image = capture_buffer->data; // 将图像数据复制到输出缓冲区中。
            output_bufferpool->set_buffer_ready(output_buffer); // 设置输出缓冲区为就绪状态。
            capture_bufferpool.set_buffer_idle(capture_buffer);

            auto infer_end = std::chrono::high_resolution_clock::now();

            std::chrono::duration<double, std::milli> fetch_buffer_time = infer_start - fetch_buffer_start;
            std::chrono::duration<double, std::milli> infer_time = infer_end - infer_start;
            LOG_I("[Inference] [ThreadId] %d, fetch %.3f ms, infer %.3f ms", std::this_thread::get_id(), fetch_buffer_time.count(), infer_time.count());
        }
    } // 推理线程函数，用于执行推理操作。

public:

    infer_threadpool(
        const string &chip,
        const string &model_path,
        rw_bufferpool<rknn_general_output<T>> * rknn_output_bufferpool,
        std::function<std::unique_ptr<rknn_model<T>>(const float box_threshold, const float nms_threshold)> &&create_model,
        const float box_threshold,
        const float nms_threshold)
        : stop_flag(false),
        infer_thread_vector(),
        models(),
        capture_bufferpool(5),
        output_bufferpool(rknn_output_bufferpool)
    {
        int npu_core_num = 1;
        if (chip == "rk3588" || chip == "rk3588s")
        {
            // There are 3 NPU cores on rk3588, so we can use 3 threads to run inference.
            npu_core_num = 3; // 设置 NPU 核心数为 3。
        }
        else if (chip == "rk3576")
        {
            // There are 2 NPU cores on rk3576, so we can use 2 threads to run inference.
            npu_core_num = 2; // 设置 NPU 核心数为 2。
        }
        LOG_I("npu_core_num = %d", npu_core_num);

        // 预分配模型vector的大小
        models.reserve(npu_core_num);

        // 创建第一个模型
        models.push_back(create_model(box_threshold, nms_threshold));
        if (models[0]->init_model(model_path) < 0)
        {
            LOG_E("Failed to initialize first model");
            return;
        }

        // 创建第一个推理线程
        infer_thread_vector.emplace_back(&infer_threadpool::infer_runnable,
                                         this, models[0].get());

        // 创建其他模型和线程
        for (int i = 1; i < npu_core_num; i++)
        {
            models.push_back(create_model(box_threshold, nms_threshold));
            if (models[i]->init_model(models[0]->get_context()) < 0)
            {
                LOG_E("Failed to initialize model");
                return;
            }
            infer_thread_vector.emplace_back(&infer_threadpool::infer_runnable,
                                             this, models[i].get());
            LOG_I("Create model success, ctx_index = %d", i);
        }
    }

    bool push_capture_frame(cv::Mat & frame)
    {
        auto *capture_buffer = capture_bufferpool.get_idle_buffer();
        if (capture_buffer == nullptr)
        {
            LOG_E("Failed to get write buffer from capture pool!");
            return false;
        }
        capture_buffer->data = frame;
        capture_bufferpool.set_buffer_ready(capture_buffer);
        return true;
    }

    void stop() {
        stop_flag.store(true);
    }

    ~infer_threadpool()
    {
        stop(); // 停止推理线程。
        for (auto &thread : infer_thread_vector)
        {
            if (thread.joinable())
            {
                thread.join();
            }
        }
    }
};
