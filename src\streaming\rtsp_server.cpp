#include "../include/common.h"
#include "../include/streaming/rtsp_server.h"
#include "../include/config/rtsp_server_config.h"
#include "../include/transport/video_transport_interface.h"
#include <gst/allocators/gstdmabuf.h>
#include <sstream>
#include <iomanip>
#include <thread>
#include <fstream>
#include <map>
#include <memory>

// RTSPMediaFactory 实现
RTSPMediaFactory::RTSPMediaFactory(const RTSPServerConfig& config) : config_(config), factory_(nullptr) {
    cache_time_ = std::chrono::steady_clock::now();

    // 初始化缓存帧为无效状态
    cached_frame_.meta.is_valid = false;
}

RTSPMediaFactory::~RTSPMediaFactory() {
    cleanup();
}

bool RTSPMediaFactory::init(video_transport::IVideoSubscriber* subscriber) {
    // 创建GStreamer媒体工厂
    factory_ = gst_rtsp_media_factory_new();
    if (!factory_) {
        LOG_E("Failed to create GStreamer media factory");
        return false;
    }
    video_subscriber_ = subscriber;

    // 禁用共享，强制为每个客户端创建新的 pipeline（解决重连问题）
    gst_rtsp_media_factory_set_shared(factory_, false);
    gst_rtsp_media_factory_set_eos_shutdown(factory_, true);  // 客户端断开时清理资源

    LOG_I("Media factory configured: shared=FALSE, eos_shutdown=TRUE");

    // 设置支持UDP和UDP组播，TCP传输协议
    GstRTSPLowerTrans protocols = GstRTSPLowerTrans(
        GST_RTSP_LOWER_TRANS_UDP |
        GST_RTSP_LOWER_TRANS_UDP_MCAST |
        GST_RTSP_LOWER_TRANS_TCP
    );
    gst_rtsp_media_factory_set_protocols(factory_, protocols);

    LOG_I("Supported protocols: UDP, UDP_MCAST, TCP");

    // 等待第一帧数据来确定视频格式
    if (!wait_for_first_frame()) {
        LOG_E("Failed to receive first frame from subscriber");
        return false;
    }

    // 根据接收到的视频格式创建pipeline描述
    std::string pipeline_desc = create_pipeline_description(config_);
    gst_rtsp_media_factory_set_launch(factory_, pipeline_desc.c_str());

    // 设置媒体配置回调
    g_signal_connect(factory_, "media-configure", G_CALLBACK(media_configure_callback), this);

    LOG_I("RTSPMediaFactory initialized");
    LOG_I("Pipeline: %s", pipeline_desc.c_str());

    return true;
}

bool RTSPMediaFactory::wait_for_first_frame() {
    LOG_D("Waiting for first frame from subscriber");

    video_transport::IVideoSubscriber::SubscriberData first_frame;
    int retry_count = 0;
    const int max_retries = 50; // 最多等待5秒 (50 * 100ms)

    while (retry_count < max_retries) {
        if (video_subscriber_->receive_frame_buffer(first_frame, 20) == video_transport::BufferResult::SUCCESS) { // 20ms超时 - 优化延时
            // 成功接收到第一帧，更新配置参数
            current_width_.store(first_frame.meta.width);
            current_height_.store(first_frame.meta.height);
            current_format_.store(first_frame.meta.format);

            LOG_I("First frame received: %dx%d format=%d, output will be: %dx%d@%dfps",
                  first_frame.meta.width, first_frame.meta.height, first_frame.meta.format,
                  first_frame.meta.width, first_frame.meta.height, config_.output_fps);
            FRAME_CACHE_TIMEOUT_MS_ = 1000 / config_.output_fps / 4;  // 缓存时间为帧率的1/4
            return true;
        }

        retry_count++;
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    LOG_E("Timeout waiting for first frame from subscriber");
    return false;
}

std::string RTSPMediaFactory::create_pipeline_description(const RTSPServerConfig& config) {
    std::ostringstream pipeline;

    // 获取当前视频参数
    int input_width = current_width_.load();
    int input_height = current_height_.load();
    int input_format = current_format_.load();

    LOG_I("Creating pipeline for format: 0x%08x (%dx%d@%dfps)",
          input_format, input_width, input_height, config.output_fps);

    // 根据输入格式分类处理
    if (input_format == V4L2_PIX_FMT_H264) {
        // H264已编码数据：简化的解析和打包pipeline - 优化延时
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"video/x-h264,width=" << input_width
                 << ",height=" << input_height
                 << ",framerate=" << config.output_fps << "/1"
                 << ",stream-format=byte-stream,alignment=au\" "
                 << "! h264parse config-interval=1 "
                 << "! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 "
                 << "aggregate-mode=zero-latency buffer-list=false )";  // 零延迟模式，禁用缓冲列表

    } else if (input_format == V4L2_PIX_FMT_H265) {
        // H265已编码数据：与H264保持完全一致的pipeline结构 - 优化延时
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"video/x-h265,width=" << input_width
                 << ",height=" << input_height
                 << ",framerate=" << config.output_fps << "/1"
                 << ",stream-format=byte-stream,alignment=au\" "
                 << "! h265parse config-interval=1 "
                 << "! rtph265pay name=pay0 pt=96 config-interval=1 mtu=1400 "
                 << "aggregate-mode=zero-latency buffer-list=false )";  // 零延迟模式，禁用缓冲列表

    } else if (input_format == V4L2_PIX_FMT_MJPEG) {
        // MJPEG数据：解码后重新编码
        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"image/jpeg,format=MJPG,width=" << input_width
                 << ",height=" << input_height
                 << ",framerate=" << config.output_fps << "/1"
                 << ",parsed=true"
                 << ",pixel-aspect-ratio=1/1"
                 << ",interlace-mode=progressive"
                 << ",colorimetry=2:4:7:1\" "
                 << "! mppjpegdec ";

        // 添加编码器
        add_encoder_pipeline(pipeline);

    } else {
        // 原始视频数据：格式转换后编码
        std::string gst_format = v4l2_format_to_gst_string(input_format);

        pipeline << "( appsrc name=source is-live=true do-timestamp=false format=time "
                 << "caps=\"video/x-raw,format=" << gst_format
                 << ",width=" << input_width
                 << ",height=" << input_height
                 << ",framerate=" << config.output_fps << "/1\" ";

        // 根据格式决定是否需要videoconvert
        if (need_format_conversion(input_format)) {
            pipeline << "! videoconvert ";
        }

        // 添加编码器
        add_encoder_pipeline(pipeline);
    }

    std::string result = pipeline.str();
    LOG_I("Generated pipeline: %s", result.c_str());
    return result;
}

void RTSPMediaFactory::add_encoder_pipeline(std::ostringstream& pipeline) {
    // 编码器选择和配置
    // 编码器选择和配置 - 分别控制H264和H265硬件编码器

    // 优先尝试H264硬件编码器
    if (config_.use_hw_encoder_h264 && RTSPServerUtils::check_hardware_encoder_support("H264")) {
        LOG_I("Using H264 hardware encoder (mpph264enc)");
        pipeline << "! mpph264enc "
                 << "bps=" << config_.output_bitrate << " "
                 << "bps-min=" << (config_.output_bitrate / 2) << " "
                 << "bps-max=" << (config_.output_bitrate * 2) << " "
                 << "profile=baseline "
                 << "gop=" << config_.gop_size << " "
                 << "rc-mode=1 "  // CBR
                 << "! h264parse "
                 << "! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        return;
    }

    // 尝试H265硬件编码器
    if (config_.use_hw_encoder_h265 && RTSPServerUtils::check_hardware_encoder_support("H265")) {
        LOG_I("Using H265 hardware encoder (mpph265enc)");
        pipeline << "! mpph265enc "
                 << "bps=" << config_.output_bitrate << " "
                 << "bps-min=" << (config_.output_bitrate / 2) << " "
                 << "bps-max=" << (config_.output_bitrate * 2) << " "
                 << "gop=" << config_.gop_size << " "
                 << "rc-mode=1 "  // CBR
                 << "! h265parse "
                 << "! rtph265pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        return;
    }

    // 硬件编码器不可用或未启用，回退到软件编码器
    LOG_I("Using software encoder (x264enc) - hardware encoders disabled or unavailable");
    add_software_encoder(pipeline);
}

void RTSPMediaFactory::add_software_encoder(std::ostringstream& pipeline) {
    // 如果用户偏好 H265，尝试软件 H265 编码器
    if (config_.use_hw_encoder_h265) {
        LOG_I("Using software H265 encoder (x265enc)");
        pipeline << "! x265enc "
                 << "bitrate=" << (config_.output_bitrate / 1000) << " "  // kbps
                 << "tune=zerolatency "
                 << "speed-preset=ultrafast "
                 << "key-int-max=" << config_.gop_size << " "
                 << "threads=" << 4 << " "
                 << "! h265parse "
                 << "! rtph265pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
    } else {
        LOG_I("Using software H264 encoder (x264enc)");
        pipeline << "! x264enc "
                 << "bitrate=" << (config_.output_bitrate / 1000) << " "  // kbps
                 << "tune=zerolatency "
                 << "speed-preset=ultrafast "
                 << "key-int-max=" << config_.gop_size << " "
                 << "threads=" << 4 << " "
                 << "! h264parse "
                 << "! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
    }
}

bool RTSPMediaFactory::need_format_conversion(int input_format) {
    // 检查是否需要格式转换
    if (!config_.use_hw_encoder_h264 && !config_.use_hw_encoder_h265) {
        // 软件编码器通常需要格式转换
        return true;
    }

    /*
    RKMPP 编码器支持的所有格式：
    MPP_FMT_YUV420SP,
    MPP_FMT_YUV420P,
    MPP_FMT_YUV422_YUYV,
    MPP_FMT_YUV422_UYVY,
    MPP_FMT_YUV444SP,
    MPP_FMT_YUV444P,
    MPP_FMT_RGB565LE,
    MPP_FMT_BGR565LE,
    MPP_FMT_RGB888,
    MPP_FMT_BGR888,
    MPP_FMT_ARGB8888,
    MPP_FMT_ABGR8888,
    MPP_FMT_RGBA8888,
    MPP_FMT_BGRA8888,
    */
    switch (input_format) {
        case V4L2_PIX_FMT_YUV420:  // I420
        case V4L2_PIX_FMT_NV12:    // NV12
        case V4L2_PIX_FMT_YUYV:    // YUY2
        case V4L2_PIX_FMT_UYVY:    // UYVY
        case V4L2_PIX_FMT_RGB24:   // RGB
        case V4L2_PIX_FMT_BGR24:   // BGR
        case V4L2_PIX_FMT_RGB32:   // RGBX
        case V4L2_PIX_FMT_BGR32:   // BGRX
            return false;  // 硬件编码器原生支持
        default:
            return true;   // 需要格式转换
    }
}

void RTSPMediaFactory::cleanup() {
    {
        std::lock_guard<std::mutex> lock(data_mutex_);
        cached_frame_.meta.is_valid = false;
    }

    if (factory_) {
        g_object_unref(factory_);
        factory_ = nullptr;
    }

    LOG_I("RTSPMediaFactory cleanup completed");
}

void RTSPMediaFactory::configure_media(GstRTSPMedia* media) {
    LOG_I("=== Media Configure Callback Triggered ===");

    // 重置缓存状态，确保新连接使用新数据
    {
        std::lock_guard<std::mutex> lock(data_mutex_);
        cached_frame_.meta.is_valid = false;
        cache_time_ = std::chrono::steady_clock::now();
        LOG_D("Reset cached frame state for new connection");
    }

    // 获取媒体的pipeline
    GstElement* pipeline = gst_rtsp_media_get_element(media);
    if (!pipeline) {
        LOG_E("Failed to get media pipeline");
        return;
    }

    LOG_I("Got media pipeline: %p", pipeline);

    // 查找appsrc元素
    GstElement* appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
    if (!appsrc) {
        LOG_E("Failed to find appsrc element named 'source'");

        // 尝试调试：列出pipeline中的所有元素
        GstIterator* iter = gst_bin_iterate_elements(GST_BIN(pipeline));
        GValue value = G_VALUE_INIT;
        GstIteratorResult result;

        LOG_I("Pipeline elements:");
        while ((result = gst_iterator_next(iter, &value)) == GST_ITERATOR_OK) {
            GstElement* element = GST_ELEMENT(g_value_get_object(&value));
            gchar* name = gst_element_get_name(element);
            gchar* factory_name = gst_plugin_feature_get_name(GST_PLUGIN_FEATURE(gst_element_get_factory(element)));
            LOG_I("  - Element: %s (factory: %s)", name, factory_name);
            g_free(name);
            g_value_reset(&value);
        }
        g_value_unset(&value);
        gst_iterator_free(iter);

        gst_object_unref(pipeline);
        return;
    }

    LOG_I("Found appsrc element: %p", appsrc);

    // 检查 appsrc 当前状态
    GstState current_state, pending_state;
    GstStateChangeReturn ret = gst_element_get_state(appsrc, &current_state, &pending_state, 0);
    LOG_I("Appsrc current state: %s, pending: %s, return: %d",
          gst_element_state_get_name(current_state),
          gst_element_state_get_name(pending_state), ret);

    // 强制重置 appsrc 状态和内部缓冲区
    LOG_I("Resetting appsrc state for new connection");

    // 1. 检查当前缓冲区状态
    guint64 current_level_bytes = 0;
    g_object_get(appsrc, "current-level-bytes", &current_level_bytes, NULL);
    if (current_level_bytes > 0) {
        LOG_W("Appsrc buffer not empty: %lu bytes, forcing cleanup", current_level_bytes);
        gst_app_src_end_of_stream(GST_APP_SRC(appsrc));
    }

    // 2. 设置为 NULL 状态
    gst_element_set_state(appsrc, GST_STATE_NULL);
    gst_element_get_state(appsrc, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 3. 清理旧的信号连接和用户数据
    g_signal_handlers_disconnect_by_data(appsrc, this);

    // 清理旧的时间戳计数器和关键帧状态（如果存在）
    g_object_set_data(G_OBJECT(appsrc), "frame-counter", NULL);
    g_object_set_data(G_OBJECT(appsrc), "waiting-keyframe", NULL);

    // 4. 重置 appsrc 内部状态
    gst_app_src_set_stream_type(GST_APP_SRC(appsrc), GST_APP_STREAM_TYPE_STREAM);
    gst_app_src_set_max_bytes(GST_APP_SRC(appsrc), 0);  // 重置缓冲区限制

    // 5. 重新设置为 READY 状态
    gst_element_set_state(appsrc, GST_STATE_READY);
    gst_element_get_state(appsrc, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 6. 为每个 appsrc 创建独立的时间戳计数器和关键帧等待状态
    uint64_t* frame_counter = new uint64_t(0);
    bool* waiting_for_keyframe = new bool(true);  // 每个客户端独立等待关键帧

    g_object_set_data_full(G_OBJECT(appsrc), "frame-counter", frame_counter,
                          [](gpointer data) { delete static_cast<uint64_t*>(data); });
    g_object_set_data_full(G_OBJECT(appsrc), "waiting-keyframe", waiting_for_keyframe,
                          [](gpointer data) { delete static_cast<bool*>(data); });

    LOG_I("*** Created independent frame counter and keyframe state for appsrc: %p ***", appsrc);

    // 7. 设置appsrc回调
    g_signal_connect(appsrc, "need-data", G_CALLBACK(need_data_callback), this);
    g_signal_connect(appsrc, "enough-data", G_CALLBACK(enough_data_callback), this);

    LOG_I("Connected appsrc signals after reset");

    // 配置appsrc属性 - 优化延时设置
    g_object_set(G_OBJECT(appsrc),
                 "is-live", TRUE,
                 "do-timestamp", TRUE,   // 自动时间戳，避免时间戳问题
                 "format", GST_FORMAT_TIME,
                 "min-latency", G_GUINT64_CONSTANT(0),         // 最小延迟
                 "max-latency", G_GUINT64_CONSTANT(50000000),  // 50ms - 减少最大延迟
                 "max-buffers", 1,          // 最大缓冲区数为1，禁用缓冲列表（优化延时）
                 "stream-type", GST_APP_STREAM_TYPE_STREAM,
                 "max-bytes", (guint64)0,     // 无限制
                 "block", FALSE,              // 非阻塞模式
                 "emit-signals", TRUE,        // 确保发出信号
                 NULL);

    // 添加 Pipeline 状态监控
    LOG_I("Adding pipeline state monitoring");

    // 监控 pipeline 状态变化
    GstBus* bus = gst_element_get_bus(pipeline);
    if (bus) {
        gst_bus_add_watch(bus, pipeline_bus_callback, this);
        gst_object_unref(bus);
    }

    // 检查所有元素状态
    check_pipeline_elements_state(pipeline);

    // 配置完成，等待 need-data 回调
    LOG_I("Appsrc configured, waiting for need-data callback to start data flow");

    gst_object_unref(appsrc);
    gst_object_unref(pipeline);

    LOG_I("Media configured successfully");
}

void RTSPMediaFactory::feed_data(GstElement* appsrc) {
    // 获取最新帧数据（使用缓存避免重复读取）
    video_transport::IVideoSubscriber::SubscriberData input_frame;
    if (!get_latest_frame(input_frame)) {
        LOG_W("No frame data available in feed_data");
        return;
    }

    LOG_D("Successfully read frame id %ld: %dx%d, size=%zu bytes, is_keyframe=%s, cost: %ld ms",
          input_frame.meta.frame_id, input_frame.meta.width, input_frame.meta.height,
           input_frame.meta.data_size, input_frame.meta.is_keyframe ? "true" : "false",
           (get_current_us() - input_frame.meta.timestamp) / 1000);

    // 对于 H264/H265 格式，检查该 appsrc 是否需要等待关键帧
    if (input_frame.meta.format == V4L2_PIX_FMT_H264 || input_frame.meta.format == V4L2_PIX_FMT_H265) {
        // 获取该 appsrc 的独立关键帧等待状态
        bool* waiting_for_keyframe = static_cast<bool*>(g_object_get_data(G_OBJECT(appsrc), "waiting-keyframe"));
        if (!waiting_for_keyframe) {
            LOG_E("Keyframe waiting state not found for appsrc: %p, creating new one", appsrc);
            waiting_for_keyframe = new bool(true);
            g_object_set_data_full(G_OBJECT(appsrc), "waiting-keyframe", waiting_for_keyframe,
                                  [](gpointer data) { delete static_cast<bool*>(data); });
        }

        if (*waiting_for_keyframe) {
            // 循环读取 subscriber 数据包，等待关键帧，超时时间 500ms - 优化延时
            if (!wait_for_keyframe(input_frame, appsrc, 500)) {
                LOG_W("Timeout waiting for keyframe on appsrc %p", appsrc);
                return;
            }

            // 找到关键帧，更新状态
            *waiting_for_keyframe = false;
            LOG_I("Found keyframe for %s on appsrc %p, starting data transmission",
                  input_frame.meta.format == V4L2_PIX_FMT_H264 ? "H264" : "H265", appsrc);
        }
        // 测试代码：保存NALU到本地文件（每个appsrc独立保存）
        // save_nalu_for_debug(appsrc, input_frame);
    }

    // 检查视频参数是否改变，如果改变需要重新创建pipeline
    if (current_width_.load() != input_frame.meta.width ||
        current_height_.load() != input_frame.meta.height ||
        current_format_.load() != input_frame.meta.format) {

        LOG_I("Video parameters changed: %dx%d (0x%08x) -> %dx%d (0x%08x)",
              current_width_.load(), current_height_.load(), current_format_.load(),
              input_frame.meta.width, input_frame.meta.height, input_frame.meta.format);

        // 更新参数
        current_width_.store(input_frame.meta.width);
        current_height_.store(input_frame.meta.height);
        current_format_.store(input_frame.meta.format);

        // 重新创建pipeline（这会在下次媒体请求时生效）
        std::string pipeline_desc = create_pipeline_description(config_);
        gst_rtsp_media_factory_set_launch(factory_, pipeline_desc.c_str());

        LOG_I("Updated pipeline description for new format");
    }

    // 直接创建GstBuffer并推送原始数据到appsrc
    // GStreamer pipeline会处理所有的格式转换和编码
    GstBuffer* buffer = gst_buffer_new_allocate(NULL, input_frame.meta.data_size, NULL);
    if (!buffer) {
        LOG_E("Failed to allocate GstBuffer of size %zu", input_frame.meta.data_size);
        return;
    }

    // DMA/SHMEM模式，使用dmabuf fd/mmap内存fd推送数据
    // if (input_frame.transport_type == video_transport::TransportType::DMA) {
    //     // 验证 fd 有效性
    //     if (input_frame.fd < 0) {
    //         LOG_E("Invalid DMA fd: %d", input_frame.fd);
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
        
    //     // 复制 fd 以确保生命周期
    //     int dup_fd = dup(input_frame.fd);
    //     if (dup_fd < 0) {
    //         LOG_E("Failed to duplicate DMA fd: %s", strerror(errno));
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
        
    //     GstAllocator *dmabuf_allocator = gst_dmabuf_allocator_new();
    //     if (!dmabuf_allocator) {
    //         LOG_E("Failed to create dmabuf allocator");
    //         close(dup_fd);
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
        
    //     GstMemory *memory = gst_dmabuf_allocator_alloc(dmabuf_allocator, dup_fd, input_frame.meta.data_size);
    //     if (!memory) {
    //         LOG_E("Failed to create dmabuf GstMemory");
    //         gst_object_unref(dmabuf_allocator);
    //         close(dup_fd);
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
    //     gst_buffer_append_memory(buffer, memory);
    //     gst_object_unref(dmabuf_allocator);
        
    // } else if (input_frame.transport_type == video_transport::TransportType::SHMEM) {
    //     // 验证 fd 有效性
    //     if (input_frame.fd < 0) {
    //         LOG_E("Invalid SHMEM fd: %d", input_frame.fd);
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
        
    //     // 复制 fd 以确保生命周期
    //     int dup_fd = dup(input_frame.fd);
    //     if (dup_fd < 0) {
    //         LOG_E("Failed to duplicate SHMEM fd: %s", strerror(errno));
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
        
    //     GstAllocator *fd_allocator = gst_fd_allocator_new();
    //     if (!fd_allocator) {
    //         LOG_E("Failed to create fd allocator");
    //         close(dup_fd);
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
        
    //     GstMemory *memory = gst_fd_allocator_alloc(fd_allocator, dup_fd, input_frame.meta.data_size, GST_FD_MEMORY_FLAG_KEEP_MAPPED);
    //     if (!memory) {
    //         LOG_E("Failed to create shmem GstMemory");
    //         gst_object_unref(fd_allocator);
    //         close(dup_fd);
    //         gst_buffer_unref(buffer);
    //         return;
    //     }
    //     gst_buffer_append_memory(buffer, memory);
    //     gst_object_unref(fd_allocator);
    // } else {
    {
        // 填充数据，使用内存数据推送数据（适用于软件编码）
        GstMapInfo map;
        if (!gst_buffer_map(buffer, &map, GST_MAP_WRITE)) {
            LOG_E("Failed to map GstBuffer");
            gst_buffer_unref(buffer);
            return;
        }

        memcpy(map.data, input_frame.data_ptr, input_frame.meta.data_size);
        gst_buffer_unmap(buffer, &map);
    }

    // 获取该 appsrc 的独立时间戳计数器
    uint64_t* frame_counter = static_cast<uint64_t*>(g_object_get_data(G_OBJECT(appsrc), "frame-counter"));
    if (!frame_counter) {
        LOG_E("Frame counter not found for appsrc: %p, creating new one", appsrc);
        frame_counter = new uint64_t(0);
        g_object_set_data_full(G_OBJECT(appsrc), "frame-counter", frame_counter,
                              [](gpointer data) { delete static_cast<uint64_t*>(data); });
    }

    // 使用独立的时间戳计数器
    uint64_t frame_num = (*frame_counter)++;
    uint64_t timestamp_ns = frame_num * GST_SECOND / config_.output_fps;  // 每个客户端从0开始
    GST_BUFFER_PTS(buffer) = timestamp_ns;
    GST_BUFFER_DTS(buffer) = timestamp_ns;
    GST_BUFFER_DURATION(buffer) = GST_SECOND / config_.output_fps;

    GstFlowReturn flow_ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc), buffer);
    if (flow_ret != GST_FLOW_OK) {
        LOG_E("Failed to push buffer to appsrc: flow_ret=%d (%s)",
              flow_ret, gst_flow_get_name(flow_ret));
        // buffer已经被appsrc接管，不需要手动释放
        return;
    }

    // 更新统计信息
    frames_served_.fetch_add(1);
}



// RTSPServerService 实现
bool RTSPServerService::init(const RTSPServerConfig& config, video_transport::IVideoSubscriber* subscriber) {
    config_ = config;

    // 设置GStreamer debug级别
    RTSPServerUtils::set_gstreamer_debug_level(config_.gst_debug_level);

    // 初始化GStreamer
    if (!RTSPServerUtils::init_gstreamer()) {
        LOG_E("Failed to initialize GStreamer");
        return false;
    }
    
    // 创建RTSP服务器
    server_ = gst_rtsp_server_new();
    if (!server_) {
        LOG_E("Failed to create RTSP server");
        return false;
    }
    
    // 配置服务器
    gst_rtsp_server_set_address(server_, config_.server_address.c_str());
    gst_rtsp_server_set_service(server_, std::to_string(config_.server_port).c_str());
    
    // 创建挂载点
    mounts_ = gst_rtsp_server_get_mount_points(server_);
    
    // 创建媒体工厂
    factory_ = std::make_unique<RTSPMediaFactory>(config_);
    if (!factory_->init(subscriber)) {
        LOG_E("Failed to initialize media factory");
        return false;
    }
    
    // 挂载媒体工厂
    gst_rtsp_mount_points_add_factory(mounts_, config_.mount_point.c_str(),
                                     factory_->get_factory());
    
    // 配置传输参数
    RTSPServerUtils::configure_rtsp_transport_params(server_);    
    // 连接信号
    g_signal_connect(server_, "client-connected", 
                     G_CALLBACK(client_connected_callback), this);
    
    LOG_I("RTSP server initialized: %s:%d%s",
          config_.server_address.c_str(), config_.server_port, config_.mount_point.c_str());

    return true;
}

bool RTSPServerService::start() {
    if (running_.load()) {
        LOG_W("RTSP server already running");
        return true;
    }

    // 启动服务器
    guint server_id = gst_rtsp_server_attach(server_, NULL);
    if (server_id == 0) {
        LOG_E("Failed to attach RTSP server");
        return false;
    }

    stop_requested_.store(false);
    server_thread_ = std::thread(&RTSPServerService::run, this);
    running_.store(true);
    start_time_ = std::chrono::steady_clock::now();

    LOG_I("RTSP server started on %s:%d%s",
          config_.server_address.c_str(), config_.server_port, config_.mount_point.c_str());

    return true;
}

void RTSPServerService::stop() {
    if (!running_.load()) {
        return;
    }

    stop_requested_.store(true);

    if (server_thread_.joinable()) {
        server_thread_.join();
    }

    if (server_) {
        g_object_unref(server_);
        server_ = nullptr;
    }

    if (mounts_) {
        g_object_unref(mounts_);
        mounts_ = nullptr;
    }

    factory_.reset();
    running_.store(false);

    LOG_I("RTSP server stopped");
}

void RTSPServerService::run() {
    GMainLoop* loop = g_main_loop_new(NULL, FALSE);

    LOG_I("RTSP server main loop started");

    while (!stop_requested_.load()) {
        // 运行GLib主循环，处理RTSP连接
        g_main_context_iteration(g_main_loop_get_context(loop), FALSE);

        // 减少休眠时间以降低延时，但避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    g_main_loop_unref(loop);
    LOG_I("RTSP server main loop stopped");
}

RTSPServerService::ServerStats RTSPServerService::get_stats() const {
    ServerStats stats;
    stats.total_connections = total_connections_.load();
    stats.active_connections = active_connections_.load();
    stats.error_count = error_count_.load();

    if (factory_) {
        stats.frames_served = factory_->get_frames_served();
        stats.clients_connected = factory_->get_clients_connected();
        stats.avg_conversion_time_ms = 0.0; // TODO: 从converter获取
    }

    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_);
    stats.uptime_seconds = duration.count();

    {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(error_mutex_));
        stats.last_error = last_error_;
    }

    return stats;
}

void RTSPServerService::print_stats() const {
    auto stats = get_stats();

    LOG_I("=== RTSP Server Statistics ===");
    LOG_I("Uptime: %.1f seconds", stats.uptime_seconds);
    LOG_I("Total connections: %lu", stats.total_connections);
    LOG_I("Active connections: %lu", stats.active_connections);
    LOG_I("Frames served: %lu", stats.frames_served);
    LOG_I("Clients connected: %lu", stats.clients_connected);
    LOG_I("Error count: %lu", stats.error_count);
    if (!stats.last_error.empty()) {
        LOG_I("Last error: %s", stats.last_error.c_str());
    }
    if (stats.avg_conversion_time_ms > 0) {
        LOG_I("Avg conversion time: %.2f ms", stats.avg_conversion_time_ms);
    }
}

bool RTSPServerService::update_bitrate(int new_bitrate) {
    if (new_bitrate < config_.min_bitrate || new_bitrate > config_.max_bitrate) {
        LOG_W("Bitrate %d out of range [%d, %d]", new_bitrate,
              config_.min_bitrate, config_.max_bitrate);
        return false;
    }

    config_.output_bitrate = new_bitrate;
    LOG_I("Updated bitrate to %d bps", new_bitrate);
    return true;
}

bool RTSPServerService::update_quality(int new_gop_size) {
    if (new_gop_size < 1 || new_gop_size > 300) {
        LOG_W("GOP size %d out of range [1, 300]", new_gop_size);
        return false;
    }

    config_.gop_size = new_gop_size;
    LOG_I("Updated GOP size to %d", new_gop_size);
    return true;
}

void RTSPServerService::handle_client_connected(GstRTSPClient* client) {
    total_connections_.fetch_add(1);
    active_connections_.fetch_add(1);

    LOG_I("=== CLIENT CONNECTED ===");
    LOG_I("Client: %p", client);
    LOG_I("Active connections: %lu", active_connections_.load());

    // 设置客户端断开回调
    g_signal_connect(client, "closed", G_CALLBACK(client_disconnected_callback), this);
}

void RTSPServerService::handle_client_disconnected(GstRTSPClient* client) {
    active_connections_.fetch_sub(1);
    LOG_I("=== CLIENT DISCONNECTED ===");
    LOG_I("Client: %p", client);
    LOG_I("Active connections: %lu", active_connections_.load());
}

// RTSPMediaFactory静态回调函数
void RTSPMediaFactory::media_configure_callback(GstRTSPMediaFactory* factory,
                                               GstRTSPMedia* media,
                                               gpointer user_data) {
    (void)factory;   // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning
    LOG_I("factory: %p, media: %p, user_data: %p", factory, media, user_data);

    RTSPMediaFactory* media_factory = static_cast<RTSPMediaFactory*>(user_data);
    if (!media_factory) {
        LOG_E("Media factory is null in media_configure_callback");
        return;
    }
    media_factory->configure_media(media);
    LOG_I("configure_media call completed");
}

void RTSPMediaFactory::need_data_callback(GstElement* appsrc, guint unused, gpointer user_data) {
    (void)unused;
    RTSPMediaFactory* media_factory = static_cast<RTSPMediaFactory*>(user_data);
    if (!media_factory) {
        LOG_E("Media factory is null in need_data_callback");
        return;
    }
    media_factory->feed_data(appsrc);
}

void RTSPMediaFactory::enough_data_callback(GstElement* appsrc, gpointer user_data) {
    (void)appsrc;    // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning
    LOG_D("enough_data_callback triggered for appsrc: %p", appsrc);

    RTSPMediaFactory* media_factory = static_cast<RTSPMediaFactory*>(user_data);
    if (media_factory) {
        // 可以在这里进行一些清理工作，但由于使用 GStreamer 共享，
        // 大部分客户端管理由 GStreamer 内部处理
        LOG_D("Client appsrc %p has enough data", appsrc);
    }
}

// RTSPServerService静态回调函数
void RTSPServerService::client_connected_callback(GstRTSPServer* server,
                                                 GstRTSPClient* client,
                                                 gpointer user_data) {
    (void)server;    // Suppress unused parameter warning
    RTSPServerService* service = static_cast<RTSPServerService*>(user_data);
    if (service) {
        service->handle_client_connected(client);
    }
}

void RTSPServerService::client_disconnected_callback(GstRTSPClient* client,
                                                    gpointer user_data) {
    LOG_D("Client disconnected callback triggered for client: %p", client);

    RTSPServerService* service = static_cast<RTSPServerService*>(user_data);
    if (service) {
        service->handle_client_disconnected(client);
    } else {
        LOG_E("Service is null in client_disconnected_callback");
    }
}

// RTSPServerUtils 实现
namespace RTSPServerUtils {

void set_gstreamer_debug_level(int level) {
    // 设置GStreamer debug级别
    // 级别: 0=NONE, 1=ERROR, 2=WARNING, 3=FIXME, 4=INFO, 5=DEBUG, 6=LOG, 7=TRACE, 9=MEMDUMP
    const char* debug_levels[] = {
        "0",  // GST_LEVEL_NONE
        "1",  // GST_LEVEL_ERROR
        "2",  // GST_LEVEL_WARNING
        "3",  // GST_LEVEL_FIXME
        "4",  // GST_LEVEL_INFO
        "5",  // GST_LEVEL_DEBUG
        "6"   // GST_LEVEL_LOG
    };

    // 限制级别范围
    if (level < 0) level = 0;
    if (level > 6) level = 6;

    // 设置环境变量
    std::string debug_str = std::string("*:") + debug_levels[level];

    // 设置全局debug级别
    gst_debug_set_threshold_from_string(debug_str.c_str(), TRUE);

    LOG_I("GStreamer debug level set to: %d (%s)", level, debug_str.c_str());

    // 可以设置特定组件的debug级别
    if (level >= 4) {
        // 对于INFO及以上级别，启用RTSP相关组件的详细日志
        gst_debug_set_threshold_for_name("rtspserver", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("rtspmedia", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("rtspstream", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("appsrc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("mpp", GST_LEVEL_DEBUG);
    }

    if (level >= 5) {
        // 对于DEBUG级别，启用编码器相关的详细日志
        gst_debug_set_threshold_for_name("mpph264enc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("mpph265enc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("mppjpegenc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("x264enc", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("h264parse", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("h265parse", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("rtph264pay", GST_LEVEL_DEBUG);
        gst_debug_set_threshold_for_name("rtph265pay", GST_LEVEL_DEBUG);
    }
}

bool init_gstreamer() {
    static bool initialized = false;
    if (initialized) {
        return true;
    }

    GError* error = nullptr;
    if (!gst_init_check(nullptr, nullptr, &error)) {
        if (error) {
            LOG_E("Failed to initialize GStreamer: %s", error->message);
            g_error_free(error);
        } else {
            LOG_E("Failed to initialize GStreamer: unknown error");
        }
        return false;
    }

    initialized = true;
    LOG_I("GStreamer initialized successfully");
    return true;
}

void optimize_gst_pipeline_for_realtime(GstElement* pipeline) {
    // 设置pipeline为实时模式
    gst_pipeline_set_latency(GST_PIPELINE(pipeline), 0);

    // 禁用缓冲
    g_object_set(G_OBJECT(pipeline), "async-handling", TRUE, NULL);
}

bool check_hardware_encoder_support(const std::string& codec) {
    GstElementFactory* factory = nullptr;

    if (codec == "H264") {
        // 优先检查mpph264enc，回退到nvh264enc
        factory = gst_element_factory_find("mpph264enc");
        if (!factory) {
            factory = gst_element_factory_find("nvh264enc");
        }
    } else if (codec == "H265") {
        // 优先检查mpph265enc，回退到nvh265enc
        factory = gst_element_factory_find("mpph265enc");
        if (!factory) {
            factory = gst_element_factory_find("nvh265enc");
        }
    }

    if (factory) {
        gst_object_unref(factory);
        return true;
    }

    return false;
}

void configure_rtsp_transport_params(GstRTSPServer* server) {
    g_object_set(server,
                 "session-timeout", 15,  // 15秒会话超时
                 NULL);

    GstRTSPSessionPool* pool = gst_rtsp_server_get_session_pool(server);
    if (pool) {
        g_object_set(pool, "max-sessions", 10, NULL);  // 简化：限制最大会话数
        g_object_unref(pool);
    }
}

void set_rtsp_buffer_sizes(GstRTSPServer* server, int buffer_size) {
    // 设置缓冲区大小
    // 注意：这些参数可能需要根据GStreamer版本调整
    g_object_set(G_OBJECT(server),
                 "backlog", buffer_size,
                 NULL);
}

} // namespace RTSPServerUtils

// ==================== 关键帧等待函数 ====================

bool RTSPMediaFactory::wait_for_keyframe(video_transport::IVideoSubscriber::SubscriberData& frame, GstElement* appsrc, int timeout_ms) {
    LOG_I("Starting keyframe wait for appsrc %p, timeout: %d ms", appsrc, timeout_ms);

    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::milliseconds(timeout_ms);

    int attempt_count = 0;

    while (true) {
        // 检查超时
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time);

        if (elapsed >= timeout_duration) {
            LOG_W("Keyframe wait timeout after %d ms, %d attempts", elapsed.count(), attempt_count);
            return false;
        }

        attempt_count++;

        // 使用统一的关键帧检测函数
        if (is_valid_keyframe(frame)) {
            LOG_I("Found valid keyframe on attempt %d after %d ms", attempt_count, elapsed.count());
            return true;
        }

        LOG_D("Attempt %d: Current frame is not keyframe, reading next frame...", attempt_count);

        // 读取下一帧，使用较短的超时时间（20ms）
        video_transport::IVideoSubscriber::SubscriberData next_frame;
        if (video_subscriber_->receive_frame_buffer(next_frame, 20) != video_transport::BufferResult::SUCCESS) {
            // 没有新数据时，检查是否应该继续等待
            auto remaining_time = timeout_duration - elapsed;
            if (remaining_time <= std::chrono::milliseconds(50)) {
                LOG_D("Insufficient time remaining (%d ms), stopping keyframe wait",
                      static_cast<int>(remaining_time.count()));
                break;
            }

            LOG_D("No new frame available, continuing wait... (remaining: %d ms)",
                  static_cast<int>(remaining_time.count()));
            std::this_thread::sleep_for(std::chrono::milliseconds(1));  // 减少等待时间以降低延时
            continue;
        }

        // 更新帧数据
        frame = std::move(next_frame);

        // 避免过于频繁的读取，定期报告进度
        if (attempt_count % 10 == 0) {
            auto current_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start_time);
            LOG_D("Keyframe wait progress: %d attempts, %d ms elapsed, %d ms remaining",
                  attempt_count, current_elapsed.count(),
                  timeout_ms - current_elapsed.count());
        }
    }

    // 超时退出
    LOG_W("Keyframe wait failed: timeout after %d attempts in %d ms", attempt_count, timeout_ms);
    return false;
}

// ==================== NALU 调试功能 ====================
void RTSPMediaFactory::save_nalu_for_debug(GstElement* appsrc, const video_transport::IVideoSubscriber::SubscriberData& frame) {
    // 为每个appsrc创建独立的调试文件和状态
    struct DebugState {
        std::ofstream file;
        bool file_opened = false;
        int frame_count = 0;
        std::string filename;
    };

    // 使用appsrc指针作为key，为每个客户端维护独立的调试状态
    static std::map<GstElement*, std::unique_ptr<DebugState>> debug_states;

    // 获取或创建该appsrc的调试状态
    auto it = debug_states.find(appsrc);
    if (it == debug_states.end()) {
        auto debug_state = std::make_unique<DebugState>();
        debug_state->filename = "debug_appsrc_" + std::to_string(reinterpret_cast<uintptr_t>(appsrc)) + ".bin";
        debug_states[appsrc] = std::move(debug_state);
        it = debug_states.find(appsrc);
        LOG_I("=== Created NALU debug state for appsrc %p ===", appsrc);
    }

    DebugState* state = it->second.get();

    // 打开文件
    if (!state->file_opened) {
        state->file.open(state->filename, std::ios::binary);
        if (state->file.is_open()) {
            LOG_I("=== NALU debug file opened: %s ===", state->filename.c_str());
            state->file_opened = true;
        } else {
            LOG_E("Failed to open debug file: %s", state->filename.c_str());
            return;
        }
    }

    // 保存数据（限制帧数避免文件过大）
    if (state->file_opened && state->frame_count < 300) {
        state->file.write(reinterpret_cast<const char*>(frame.data_ptr), frame.meta.data_size);
        state->file.flush();

        state->frame_count++;
        LOG_I("=== Saved frame %d for appsrc %p: size=%zu, keyframe=%s ===",
              state->frame_count, appsrc, frame.meta.data_size,
              frame.meta.is_keyframe ? "true" : "false");

        // 打印前16字节用于格式分析
        if (frame.meta.data_size >= 16) {
            const uint8_t* data = reinterpret_cast<const uint8_t*>(frame.data_ptr);
            LOG_I("=== First 16 bytes: %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x ===",
                  data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8], data[9], data[10], data[11], data[12], data[13], data[14], data[15]);
        }

        if (state->frame_count == 300) {
            LOG_I("=== NALU debug: reached 300 frames for appsrc %p, closing file ===", appsrc);
            state->file.close();
        }
    }
}

// ==================== 关键帧检测辅助函数 ====================

bool RTSPMediaFactory::is_valid_keyframe(const video_transport::IVideoSubscriber::SubscriberData& frame) {
    if (frame.meta.format == V4L2_PIX_FMT_H264) {
        // H264：直接使用FFmpeg的关键帧标志
        return frame.meta.is_keyframe;
    } else if (frame.meta.format == V4L2_PIX_FMT_H265) {
        // H265：必须检查NALU类型，只接受IDR帧
        if (frame.meta.is_keyframe && frame.meta.data_size > 4) {
            const uint8_t* data = reinterpret_cast<const uint8_t*>(frame.data_ptr);            
            uint32_t start_code_offset = 0;

            // 检查起始码
            if (data[0] == 0 && data[1] == 0 && data[2] == 0 && data[3] == 1) {
                start_code_offset = 4;
            } else if (data[0] == 0 && data[1] == 0 && data[2] == 1) {
                start_code_offset = 3;
            }

            if (start_code_offset > 0 && start_code_offset < frame.meta.data_size) {
                uint8_t nalu_header = data[start_code_offset];
                uint8_t nalu_type = (nalu_header >> 1) & 0x3F;

                // 只接受IDR帧 (type 19, 20)
                if (nalu_type == 19 || nalu_type == 20) {
                    LOG_D("H265 IDR frame detected (type %d)", nalu_type);
                    return true;
                } else {
                    LOG_D("H265 keyframe but not IDR (type %d)", nalu_type);
                    return false;
                }
            }
        }
        return false;  // H265非IDR帧或数据无效
    } else {
        // 其他格式：使用FFmpeg的关键帧标志
        return frame.meta.is_keyframe;
    }
}

// ==================== Pipeline 监控辅助函数 ====================

// Pipeline 状态监控回调
gboolean RTSPMediaFactory::pipeline_bus_callback(GstBus* bus, GstMessage* message, gpointer user_data) {
    (void)bus;    // Suppress unused parameter warning
    (void)message; // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning

    switch (GST_MESSAGE_TYPE(message)) {
        case GST_MESSAGE_STATE_CHANGED: {
            GstState old_state, new_state, pending_state;
            gst_message_parse_state_changed(message, &old_state, &new_state, &pending_state);

            gchar* src_name = gst_element_get_name(GST_MESSAGE_SRC(message));
            LOG_I("Pipeline element %s: %s → %s (pending: %s)",
                  src_name,
                  gst_element_state_get_name(old_state),
                  gst_element_state_get_name(new_state),
                  gst_element_state_get_name(pending_state));

            // 特别关注 appsrc 的状态变化
            if (g_str_has_prefix(src_name, "source") && new_state == GST_STATE_PLAYING) {
                LOG_I("*** Appsrc reached PLAYING state - should trigger need-data soon ***");
            }

            g_free(src_name);
            break;
        }
        case GST_MESSAGE_ERROR: {
            GError* error;
            gchar* debug;
            gst_message_parse_error(message, &error, &debug);
            LOG_E("Pipeline error: %s (debug: %s)", error->message, debug);
            g_error_free(error);
            g_free(debug);
            break;
        }
        default:
            break;
    }

    return TRUE;  // 继续监听
}

// 检查 Pipeline 中所有元素的状态
void RTSPMediaFactory::check_pipeline_elements_state(GstElement* pipeline) {
    LOG_I("=== Checking pipeline elements state ===");

    GstIterator* iter = gst_bin_iterate_elements(GST_BIN(pipeline));
    GValue value = G_VALUE_INIT;

    while (gst_iterator_next(iter, &value) == GST_ITERATOR_OK) {
        GstElement* element = GST_ELEMENT(g_value_get_object(&value));
        GstState state, pending;
        gst_element_get_state(element, &state, &pending, 0);

        gchar* name = gst_element_get_name(element);
        LOG_I("  Element %s: state=%s, pending=%s",
              name, gst_element_state_get_name(state),
              gst_element_state_get_name(pending));

        // 特别检查 appsrc 的缓冲区状态
        if (g_str_has_prefix(name, "source")) {
            guint64 current_level_bytes = 0;
            guint current_level_buffers = 0;
            g_object_get(element,
                        "current-level-bytes", &current_level_bytes,
                        "current-level-buffers", &current_level_buffers,
                        NULL);
            LOG_I("    *** Appsrc buffer status: %lu bytes, %u buffers ***",
                  current_level_bytes, current_level_buffers);
        }

        g_free(name);
        g_value_reset(&value);
    }

    g_value_unset(&value);
    gst_iterator_free(iter);
    LOG_I("=== Pipeline elements check completed ===");
}

// ==================== 按需数据获取实现 ====================

bool RTSPMediaFactory::get_latest_frame(video_transport::IVideoSubscriber::SubscriberData& frame) {
    // 移除缓存机制，直接读取最新帧以减少延时
    // 直接从 subscriber 读取最新帧，不使用缓存
    if (video_subscriber_->receive_frame_buffer(frame, 60) != video_transport::BufferResult::SUCCESS) {  // 60ms 超时 - 优化延时
        LOG_W("No new frame data available (timeout: 60ms)");
        return false;
    }

    // LOG_D("Read new frame from subscriber: %dx%d, format=0x%08x, size=%zu bytes",
        //   frame.meta.width, frame.meta.height, frame.meta.format, frame.meta.data_size);

    return true;
}
