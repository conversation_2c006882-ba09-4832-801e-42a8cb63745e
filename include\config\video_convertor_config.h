#ifndef CONVERTOR_CONFIG_H
#define CONVERTOR_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#include <cstdint>
#include <string>

// Video converter configuration
struct VideoConverterConfig {
    bool enable_hardware_acceleration = true;

    // Processing control
    bool enable_ai = true;
    bool enable_cloud_streaming = true;

    // DDS config
    std::string input_topic = "Video_Frames";
    std::string ai_output_topic = "AI_Frames";
    std::string cloud_output_topic = "Cloud_Frames";
    int domain_id = 0;
    int input_max_samples = 5;
    int output_max_samples = 5;

    // AI output config
    std::string ai_format = "RGB24";
    int ai_width = 640;
    int ai_height = 640;
    bool ai_enable_resize = true;
    std::string ai_resize_algorithm = "bilinear";

    // Cloud output config
    std::string cloud_format = "H264";
    int cloud_width = 1280;
    int cloud_height = 720;
    int cloud_bitrate = 2000000;
    int cloud_fps = 30;
    int cloud_gop_size = 15;
    std::string cloud_profile = "baseline";
    std::string cloud_preset = "ultrafast";
    std::string cloud_tune = "zerolatency";

    // Hardware acceleration config
    bool enable_gpu = true;
    bool enable_vaapi = true;
    bool enable_nvenc = true;
    bool enable_qsv = false;
    bool fallback_to_software = true;

    // Performance config
    int thread_priority = 80;
    int thread_pool_size = 2;
    int stats_interval_sec = 15;
    bool enable_zero_copy = true;
    int buffer_pool_size = 10;

    // Quality control
    bool enable_adaptive_quality = true;
    int min_quality = 20;
    int max_quality = 95;
    int target_fps = 30;
    float drop_frame_threshold = 0.1f;
};

#ifdef __cplusplus
}
#endif

#endif // CONVERTOR_CONFIG_H    