#ifndef FASTDDS_VIDEO_TRANSPORT_H
#define FASTDDS_VIDEO_TRANSPORT_H

#include "video_transport_interface.h"
#include "dds_video_writer.hpp"
#include "dds_video_reader.hpp"
#include "../common.h"
#include <atomic>
#include <mutex>
#include <memory>
#include <vector>

namespace fastdds {
namespace video {

class DDSVideoWriter {
private:
    std::unique_ptr<eprosima::fastdds::video::DDSWriter> writer_;

public:
    DDSVideoWriter(const std::string & topic_name, int domain_id, int max_samples = 3) {
        writer_ = std::make_unique<eprosima::fastdds::video::DDSWriter>();
        if (!writer_->init(topic_name, domain_id, max_samples)) {
            throw std::runtime_error("Failed to initialize DDS Video Writer");
        }
    }
    ~DDSVideoWriter() = default;
    bool write(const DDSVideoFrame & dds_frame) {
        return writer_->write(const_cast<DDSVideoFrame&>(dds_frame)) == eprosima::fastdds::dds::RETCODE_OK;
    }
};

class DDSVideoReader {
private:
    std::unique_ptr<eprosima::fastdds::video::DDSReader> reader_;
    ThreadSafeQueue<DDSVideoFrame> frame_queue_;
    std::function<void(const DDSVideoFrame&)> frame_callback_;

public:
    DDSVideoReader(const std::string & topic_name, int domain_id, int max_samples = 3,
        std::function<void(const DDSVideoFrame&)> frame_callback = nullptr): frame_queue_(max_samples), frame_callback_(frame_callback) {
        reader_ = std::make_unique<eprosima::fastdds::video::DDSReader>();
        if (!reader_->init(topic_name, domain_id, max_samples, [this](const DDSVideoFrame& dds_frame) {
            if (frame_callback_) {
                frame_callback_(dds_frame);
            } else {
                frame_queue_.push(std::move(dds_frame));
            }
        })) {
            throw std::runtime_error("Failed to initialize DDS Video Reader");
        }
    }
    ~DDSVideoReader() = default;
    bool read(DDSVideoFrame & frame, int timeout_ms = -1) {
        return frame_queue_.wait_and_pop(frame, timeout_ms);
    }

    void set_frame_callback(std::function<void(const DDSVideoFrame&)> callback) {
        frame_callback_ = callback;
    }
};
}
}

namespace video_transport {
using namespace fastdds::video;
// FastDDS视频发布者
class FastDDSVideoPublisher : public IVideoPublisher {
private:
    // DDS组件
    std::unique_ptr<DDSVideoWriter> dds_writer_;
    
    // 配置和状态
    std::atomic<bool> initialized_;  // 初始化标志，用于指示发布者是否已初始化
    std::atomic<int> subscriber_count_;  // 订阅者计数器，用于跟踪订阅者数量
    DDSVideoFrame send_frame_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    TransportStats stats_;

public:
    FastDDSVideoPublisher(const TransportConfig& config) : IVideoPublisher(config), initialized_(false), subscriber_count_(0) {}  // 默认构造函数，用于初始化发布者对象
    
    ~FastDDSVideoPublisher() override {
        cleanup();
    }
    
    // IVideoPublisher接口实现
    bool initialize() override {
        if (initialized_.load()) {
            return true;
        }

        if (config_.type != TransportType::FASTDDS) {
            LOG_E("Invalid config type for FastDDS transport");
            return false;
        }
        
        try {
            // 创建DDS Writer
            dds_writer_ = std::make_unique<DDSVideoWriter>(config_.topic_name, config_.domain_id, config_.max_samples);
            if (!dds_writer_) {
                LOG_E("Failed to initialize DDS writer");
                return false;
            }
            
            initialized_.store(true);
            reset_stats();
            
            LOG_I("FastDDS Video Publisher initialized successfully on topic: %s", 
                  config_.topic_name.c_str());
            
            return true;
        } catch (const std::exception& e) {
            LOG_E("Exception during FastDDS Publisher initialization: %s", e.what());
            return false;
        }
    }
    
    void cleanup() override {
        if (initialized_.load()) {
            dds_writer_.reset();
            initialized_.store(false);
        }
    }
    
    // 核心缓冲区管理接口
    BufferResult acquire_buffer(PublisherData& data) override {
        if (!initialized_.load()) {
            return BufferResult::TRANSPORT_ERROR;
        }

        data.transport_type = TransportType::FASTDDS;

        return BufferResult::SUCCESS;
    }

    BufferResult publish_buffer(PublisherData& pub_data, bool broadcast) override {
        if (!initialized_.load()) {
            return BufferResult::TRANSPORT_ERROR;
        }

        if (pub_data.transport_type != TransportType::FASTDDS) {
            return BufferResult::INVALID_DATA;
        }

        if (!broadcast) {
            return BufferResult::SUCCESS;
        }

        try {
            send_frame_.frame_id(pub_data.meta.frame_id);
            send_frame_.timestamp(pub_data.meta.timestamp);
            send_frame_.width(pub_data.meta.width);
            send_frame_.height(pub_data.meta.height);
            send_frame_.format(pub_data.meta.format);
            send_frame_.is_keyframe(pub_data.meta.is_keyframe);
            send_frame_.data_length(pub_data.meta.data_size);
            
            // 使用vector来存储数据
            std::vector<uint8_t> data_vector(static_cast<const uint8_t*>(pub_data.data_ptr), 
                                           static_cast<const uint8_t*>(pub_data.data_ptr) + pub_data.meta.data_size);
            send_frame_.data(data_vector);

            bool success = dds_writer_->write(send_frame_);

            update_stats_sent(pub_data.meta.data_size, success);

            return success ? BufferResult::SUCCESS : BufferResult::TRANSPORT_ERROR;

        } catch (const std::exception& e) {
            LOG_E("Exception during frame publishing: %s", e.what());
            return BufferResult::TRANSPORT_ERROR;
        }
    }
    
    // V4L2 DMABUF集成 - FastDDS不支持零拷贝
    int get_dma_fd(const PublisherData& pub_data) override {
        // FastDDS使用数据复制模式，不支持DMA文件描述符
        (void) pub_data;
        return -1;
    }

    // V4L2零拷贝支持检查
    bool supports_v4l2_zero_copy() const override {
        return false; // FastDDS不支持零拷贝
    }
    
    bool has_subscribers() const override {
        return subscriber_count_.load() > 0;
    }
    
    TransportStats get_stats() const override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return stats_;
    }
    
    void reset_stats() override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_ = TransportStats();
        stats_.last_update_time.store(get_current_us());
    }
    
    std::string get_status() const override {
        std::ostringstream oss;
        oss << "FastDDS Publisher Status:\n";
        oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\n";
        oss << "  Topic Name: " << config_.topic_name << "\n";
        oss << "  Domain ID: " << config_.domain_id << "\n";
        oss << "  Subscriber Count: " << subscriber_count_.load() << "\n";
        
        auto stats = get_stats();
        oss << "  Frames Sent: " << stats.frames_sent.load() << "\n";
        oss << "  Bytes Sent: " << stats.bytes_sent.load() << "\n";
        oss << "  Dropped Frames: " << stats.dropped_frames.load();
        
        return oss.str();
    }

private:
    void update_stats_sent(size_t bytes, bool success) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        if (success) {
            stats_.frames_sent.fetch_add(1);
            stats_.bytes_sent.fetch_add(bytes);
        } else {
            stats_.dropped_frames.fetch_add(1);
        }
        stats_.last_update_time.store(get_current_us());
    }
    
    uint64_t get_current_us() const {
        auto now = std::chrono::steady_clock::now().time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(now).count();
    }
};

// FastDDS视频订阅者 - 改进版本
class FastDDSVideoSubscriber : public IVideoSubscriber {
private:
    // DDS组件
    std::unique_ptr<DDSVideoReader> dds_reader_;
    
    // 配置和状态
    std::atomic<bool> initialized_;  // 初始化标志，用于指示发布者是否已初始化
    std::atomic<bool> connected_;  // 连接标志，用于指示订阅者是否已连接
    
    // 回调函数
    std::function<void(SubscriberData&)> buffer_callback_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    TransportStats stats_;

public:
    FastDDSVideoSubscriber(const TransportConfig& config) : IVideoSubscriber(config), initialized_(false), connected_(false) {}  // 默认构造函数，用于初始化订阅者对象
    
    ~FastDDSVideoSubscriber() override {
        cleanup();
    }
    
    // IVideoSubscriber接口实现
    bool initialize() override {
        if (initialized_.load()) {
            return true;
        }
        
        if (config_.type != TransportType::FASTDDS) {
            LOG_E("Invalid config type for FastDDS transport");
            return false;
        }
        
        try {
            // 创建DDS Reader
            dds_reader_ = std::make_unique<DDSVideoReader>(config_.topic_name, config_.domain_id, config_.max_samples);
            if (!dds_reader_) {
                LOG_E("Failed to initialize DDS reader");
                return false;
            }
            
            initialized_.store(true);
            connected_.store(true);
            reset_stats();
            
            LOG_I("FastDDS Video Subscriber initialized successfully on topic: %s", 
                  config_.topic_name.c_str());
            
            return true;
        } catch (const std::exception& e) {
            LOG_E("Exception during FastDDS Subscriber initialization: %s", e.what());
            return false;
        }
    }
    
    void cleanup() override {
        if (initialized_.load()) {
            dds_reader_.reset();
            initialized_.store(false);
            connected_.store(false);
        }
    }
    
    // 缓冲区管理接口 - FastDDS使用数据复制模式
    BufferResult receive_frame_buffer(SubscriberData& sub_data, int timeout_ms) override {
        // 尝试从队列获取帧
        DDSVideoFrame frame;
        if (!dds_reader_->read(frame, timeout_ms)) {
            return BufferResult::TIMEOUT;
        }

        // 将Frame数据包装为SubscriberData
        sub_data.meta.frame_id = frame.frame_id();
        sub_data.meta.timestamp = frame.timestamp();
        sub_data.meta.width = frame.width();
        sub_data.meta.height = frame.height();
        sub_data.meta.format = frame.format();
        sub_data.meta.data_size = frame.data().size();
        sub_data.meta.is_keyframe = frame.is_keyframe();
        sub_data.meta.is_valid = true; // Assuming frame is valid if we received it
        sub_data.transport_type = TransportType::FASTDDS;
        
        // Copy data to internal buffer
        const auto& frame_data = frame.data();
        sub_data.data_ptr = const_cast<uint8_t*>(frame_data.data());

        return BufferResult::SUCCESS;
    }
    
    void set_buffer_callback(std::function<void(SubscriberData&)> callback) override {
        buffer_callback_ = std::move(callback);
        dds_reader_->set_frame_callback([this](const DDSVideoFrame& frame) {
            on_dds_frame_received(frame);
        });
    }
    
    bool is_connected() const override {
        return connected_.load();
    }
    
    TransportStats get_stats() const override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return stats_;
    }
    
    void reset_stats() override {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_ = TransportStats();
        stats_.last_update_time.store(get_current_us());
    }
    
    std::string get_status() const override {
        std::ostringstream oss;
        oss << "FastDDS Subscriber Status:\n";
        oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\n";
        oss << "  Connected: " << (connected_.load() ? "Yes" : "No") << "\n";
        oss << "  Topic Name: " << config_.topic_name << "\n";
        oss << "  Domain ID: " << config_.domain_id << "\n";
        
        auto stats = get_stats();
        oss << "  Frames Received: " << stats.frames_received.load() << "\n";
        oss << "  Bytes Received: " << stats.bytes_received.load();
        
        return oss.str();
    }

private:
    void on_dds_frame_received(const DDSVideoFrame& dds_frame) {
        try {
            // 如果设置了缓冲区回调，创建SubscriberData并调用
            if (buffer_callback_) {
                // 转换DDS帧到内部Frame格式
                SubscriberData sub_data;
                sub_data.meta.frame_id = dds_frame.frame_id();
                sub_data.meta.timestamp = dds_frame.timestamp();
                sub_data.meta.width = dds_frame.width();
                sub_data.meta.height = dds_frame.height();
                sub_data.meta.format = dds_frame.format();
                sub_data.meta.data_size = dds_frame.data().size();                
                sub_data.meta.is_keyframe = dds_frame.is_keyframe();
                sub_data.meta.is_valid = true; // Assuming frame is valid if we received it
                
                // Copy data to internal buffer
                const auto& frame_data = dds_frame.data();
                sub_data.data_ptr = const_cast<uint8_t*>(frame_data.data());                
                
                sub_data.transport_type = TransportType::FASTDDS;
                buffer_callback_(sub_data);
            }

            update_stats_received(dds_frame.data().size());

        } catch (const std::exception& e) {
            LOG_E("Exception in FastDDS frame callback: %s", e.what());
        }
    }
    
    void update_stats_received(size_t bytes) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.frames_received.fetch_add(1);
        stats_.bytes_received.fetch_add(bytes);
        stats_.last_update_time.store(get_current_us());
    }
    
    uint64_t get_current_us() const {
        auto now = std::chrono::steady_clock::now().time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(now).count();
    }
};

} // namespace video_transport

#endif // FASTDDS_VIDEO_TRANSPORT_H