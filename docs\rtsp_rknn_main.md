# RTSP RKNN Main

这个程序通过 RTSP 客户端获取视频流，使用 MPP 解码器解码 H264/H265 NALU 数据，然后通过 RKNN 进行目标检测和跟踪，最后将结果通过 DMA IVideoPublisher 发送。

## 配置结构

程序使用专门的 `RTSPRKNNConfig` 结构体来管理配置，包含以下主要部分：

### RTSP 配置
- `url`: RTSP 流地址
- `rtsp_transport`: 传输协议（udp/tcp）
- `rtsp_timeout`: 连接超时时间（微秒）
- `decode_method`: 解码方法（mpp/gstreamer）

### 输出配置
- `output_topic_name`: 输出主题名称
- `output_transport_type`: 输出传输类型
- `output_ring_buffer_size`: 环形缓冲区大小
- `output_width/height`: 输出分辨率
- `output_format`: 输出像素格式

### RKNN 模型配置
- `model_path`: 模型文件路径
- `chip`: 芯片类型
- `box_threshold/nms_threshold`: 检测阈值
- `track_buffer/frame_rate`: 跟踪参数

### 可视化配置
- `draw_boxes/draw_labels/draw_track_ids`: 绘制选项

## 功能特性

- **RTSP 流接收**: 支持 UDP/TCP 传输协议
- **双解码方案**:
  - **MPP 方案**: 使用 RTSP 客户端 + MPP 解码器解码 H264/H265 NALU 数据
  - **GStreamer 方案**: 使用 GStreamer 管道，pph264dec 解码器，latency=0
- **格式转换**: 使用 RGA 加速器将 YUV420_SP 转换为 BGR 格式
- **目标检测**: 使用 RKNN 进行 YOLO11 目标检测
- **目标跟踪**: 支持 ByteTrack 和 BoTSORT 跟踪算法
- **结果发布**: 通过 DMA/SHMEM/FastDDS 发布跟踪结果

## 使用方法

### 基本用法

```bash
# 使用默认配置
./rtsp_rknn_main -u rtsp://*************:8554/stream

# 使用 TCP 传输
./rtsp_rknn_main -u rtsp://*************:8554/stream --tcp

# 使用 GStreamer 解码
./rtsp_rknn_main -u rtsp://*************:8554/stream --decode-method gstreamer

# 指定配置文件
./rtsp_rknn_main -c /opt/video_service/config/rtsp_rknn_track.json -u rtsp://*************:8554/stream
```

### 命令行参数

- `-c, --config FILE`: 配置文件路径
- `-u, --rtsp-url URL`: RTSP 流 URL（必需）
- `-o, --output-topic TOPIC`: 输出主题名称（默认: main_rknn_tracked_frames）
- `--output-transport TYPE`: 输出传输类型（DMA/SHMEM/FASTDDS，默认: DMA）
- `-m, --model PATH`: RKNN 模型文件路径
- `--chip TYPE`: 芯片类型（默认: rk3576）
- `--tcp`: 使用 TCP 进行 RTSP 传输（默认: UDP）
- `--decode-method METHOD`: 解码方法（mpp/gstreamer，默认: mpp）
- `--help`: 显示帮助信息

### 配置文件

配置文件使用 JSON 格式，示例：

```json
{
    "url": "rtsp://admin:123456@***************/live0",
    "rtsp_transport": "udp",
    "rtsp_timeout": 5000000,
    "decode_method": "gstreamer",
    "output_topic_name": "rtsp_rknn_tracked_frames",
    "output_transport_type": "DMA",
    "output_ring_buffer_size": 3,
    "output_width": 1920,
    "output_height": 1080,
    "output_format": "BGR24",
    "fastdds_domain_id": 0,
    "fastdds_max_samples": 5,
    "model_path": "/opt/video_service/models/yolo11s_rk3576.rknn",
    "chip": "rk3576",
    "box_threshold": 0.5,
    "nms_threshold": 0.45,
    "track_buffer": 60,
    "frame_rate": 30,
    "draw_boxes": true,
    "draw_labels": true,
    "draw_track_ids": true,
    "debug_level": 2,
    "stats_interval": 10
}
```

## 系统要求

- RK3576/RK3588 芯片
- MPP 硬件解码器支持
- RGA 硬件加速器支持
- RKNN Runtime 库
- OpenCV 库
- FFmpeg 库

## 工作流程

### MPP 方案
1. **RTSP 连接**: 连接到指定的 RTSP 流
2. **NALU 接收**: 接收 H264/H265 NALU 数据包
3. **MPP 解码**: 使用 MPP 解码器解码为 YUV420_SP 格式
4. **格式转换**: 使用 RGA 转换为 BGR 格式
5. **目标检测**: 使用 RKNN 进行 YOLO11 检测
6. **目标跟踪**: 使用跟踪算法进行目标跟踪
7. **结果发布**: 将跟踪结果发布到指定主题

### GStreamer 方案
1. **管道创建**: 创建 GStreamer 管道
2. **RTSP 接收**: rtspsrc 接收 RTSP 流
3. **H264 解包**: rtph264depay 解包 RTP 数据
4. **硬件解码**: pph264dec 硬件解码（latency=0）
5. **格式转换**: videoconvert 转换为 BGR 格式
6. **帧回调**: appsink 回调处理帧数据
7. **目标检测**: 使用 RKNN 进行 YOLO11 检测
8. **目标跟踪**: 使用跟踪算法进行目标跟踪
9. **结果发布**: 将跟踪结果发布到指定主题

## 注意事项

- 确保 RTSP 流格式为 H264 或 H265
- 程序会自动检测编码格式并初始化相应的解码器
- 支持关键帧检测，提高解码稳定性
- 使用内存拷贝确保 NALU 数据的完整性
- 支持优雅关闭（Ctrl+C）

## 故障排除

1. **RTSP 连接失败**: 检查网络连接和 RTSP URL
2. **解码失败**: 确保视频格式为 H264/H265
3. **跟踪器初始化失败**: 检查模型文件路径和权限
4. **内存不足**: 调整缓冲区大小配置
