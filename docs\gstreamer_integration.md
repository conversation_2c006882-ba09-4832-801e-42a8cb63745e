# GStreamer 集成方案

## 概述

为了解决 MPP 解码器可能出现的花屏问题，在 `rtsp_rknn_main.cpp` 中增加了 GStreamer 作为替代解码方案。用户可以通过配置文件或命令行参数在两种方案之间切换。

## 两种解码方案对比

### MPP 方案（原方案）
- **组件**: RTSPClient + MPPDecoder + RGA
- **流程**: RTSP → NALU → MPP解码 → YUV420_SP → RGA转换 → BGR → RKNN
- **优点**: 直接硬件解码，性能高
- **缺点**: 可能出现花屏问题

### GStreamer 方案（新增）
- **组件**: GStreamer Pipeline
- **流程**: RTSP → pph264dec → BGR → RKNN
- **管道**: `rtspsrc ! rtph264depay ! pph264dec ! videoconvert ! appsink`
- **优点**: 稳定性好，latency=0 低延迟
- **缺点**: 依赖 GStreamer 库

## 配置方式

### 配置文件
```json
{
    "decode_method": "gstreamer",  // 或 "mpp"
    "url": "rtsp://admin:123456@***************/live0",
    "rtsp_transport": "udp"
}
```

### 命令行参数
```bash
# 使用 GStreamer 方案
./rtsp_rknn_main -u rtsp://your-url --decode-method gstreamer

# 使用 MPP 方案（默认）
./rtsp_rknn_main -u rtsp://your-url --decode-method mpp
```

## 技术实现

### GStreamer 管道结构
```
rtspsrc location=<url> protocols=<tcp/udp> latency=0
  ↓
rtph264depay
  ↓
pph264dec (硬件解码器)
  ↓
videoconvert
  ↓
video/x-raw,format=BGR
  ↓
appsink name=sink sync=false max-buffers=1 drop=true
```

### 关键特性
- **latency=0**: 最小化延迟
- **pph264dec**: 使用硬件 H264 解码器
- **max-buffers=1**: 最小缓冲，降低延迟
- **drop=true**: 丢弃旧帧，保持实时性

### 回调处理
- GStreamer appsink 回调函数处理解码后的帧
- 直接将 BGR 数据复制到发布缓冲区
- 推送到 RKNN 进行目标检测和跟踪

## 使用建议

1. **默认使用 MPP 方案**: 性能更好
2. **出现花屏时切换到 GStreamer**: 稳定性更好
3. **网络不稳定时使用 TCP 传输**: `--tcp` 参数
4. **调试时启用详细日志**: 设置 debug_level

## 故障排除

### MPP 方案问题
- **花屏**: 切换到 GStreamer 方案
- **解码失败**: 检查视频格式是否为 H264/H265
- **内存泄漏**: 检查 NALU 数据复制和释放

### GStreamer 方案问题
- **管道创建失败**: 检查 GStreamer 插件是否安装
- **pph264dec 不可用**: 确认硬件解码器支持
- **帧率不稳定**: 调整 max-buffers 参数

## 性能对比

| 方案 | 延迟 | CPU 使用率 | 内存使用 | 稳定性 |
|------|------|------------|----------|--------|
| MPP | 低 | 低 | 中 | 中 |
| GStreamer | 低 | 中 | 中 | 高 |

## 依赖要求

### MPP 方案
- rockchip_mpp 库
- RGA 库
- FFmpeg 库

### GStreamer 方案
- GStreamer 1.0
- gst-plugins-base
- gst-plugins-good
- gst-plugins-bad (pph264dec)

## 未来优化

1. **自动切换**: 检测解码错误自动切换方案
2. **性能监控**: 添加帧率、延迟监控
3. **更多解码器**: 支持其他硬件解码器
4. **动态配置**: 运行时切换解码方案
