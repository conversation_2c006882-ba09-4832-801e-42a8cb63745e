Jan  1 03:26:48 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6374
Jan  1 03:26:48 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6374: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12154031 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.478 ms, Inference: 42.240 ms, Post-processing: 2.614 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1840043328, fetch 54.729 ms, infer 48.382 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.131 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6375, delay: 12154004 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6377, cost: 48 ms, rx: 42 ms, convert: 5 ms
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6375
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6375: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12154027 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.910 ms, Inference: 36.724 ms, Post-processing: 1.741 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1916581184, fetch 0.012 ms, infer 42.437 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.443 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6376, delay: 12153995 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6376
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6376: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12154009 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6378, cost: 30 ms, rx: 25 ms, convert: 4 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 4.273 ms, Inference: 40.208 ms, Post-processing: 2.283 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1840043328, fetch 22.498 ms, infer 46.880 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.280 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6377, delay: 12153995 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6377
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6377: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12153995 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6379, cost: 40 ms, rx: 35 ms, convert: 5 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.952 ms, Inference: 37.479 ms, Post-processing: 1.441 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1916581184, fetch 23.511 ms, infer 42.924 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.215 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6378, delay: 12153980 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6378
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6378: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12153981 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6380, cost: 38 ms, rx: 33 ms, convert: 4 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.643 ms, Inference: 34.555 ms, Post-processing: 2.498 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1840043328, fetch 24.148 ms, infer 40.771 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.202 ms
Jan  1 03:26:49 rk3576-buildroot user.warn mpp[13830]: No new frame data for 1 consecutive attempts on appsrc 0x7f68019410
Jan  1 03:26:49 rk3576-buildroot user.warn mpp[13830]: No frame data available and no cached frame for appsrc 0x7f68019410 (attempt 1)
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6379, delay: 12153979 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.202 ms, Inference: 32.130 ms, Post-processing: 1.226 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1916581184, fetch 36.353 ms, infer 36.605 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.193 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6380, delay: 12153974 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6381, cost: 40 ms, rx: 35 ms, convert: 4 ms





Jan  1 03:26:48 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6374
Jan  1 03:26:48 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6374: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12154031 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.478 ms, Inference: 42.240 ms, Post-processing: 2.614 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1840043328, fetch 54.729 ms, infer 48.382 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.131 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6375, delay: 12154004 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6377, cost: 48 ms, rx: 42 ms, convert: 5 ms
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6375
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6375: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12154027 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.910 ms, Inference: 36.724 ms, Post-processing: 1.741 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1916581184, fetch 0.012 ms, infer 42.437 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.443 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6376, delay: 12153995 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6376
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6376: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12154009 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6378, cost: 30 ms, rx: 25 ms, convert: 4 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 4.273 ms, Inference: 40.208 ms, Post-processing: 2.283 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1840043328, fetch 22.498 ms, infer 46.880 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.280 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6377, delay: 12153995 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6377
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6377: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12153995 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6379, cost: 40 ms, rx: 35 ms, convert: 5 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.952 ms, Inference: 37.479 ms, Post-processing: 1.441 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1916581184, fetch 23.511 ms, infer 42.924 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.215 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6378, delay: 12153980 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Read new frame from subscriber: 1920x1080, format=0x33524742, size=6220800 bytes, frame_id=6378
Jan  1 03:26:49 rk3576-buildroot user.debug mpp[13830]: Successfully read frame id 6378: 1920x1080, size=6220800 bytes, is_keyframe=true, cost: 12153981 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6380, cost: 38 ms, rx: 33 ms, convert: 4 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.643 ms, Inference: 34.555 ms, Post-processing: 2.498 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1840043328, fetch 24.148 ms, infer 40.771 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.202 ms
Jan  1 03:26:49 rk3576-buildroot user.warn mpp[13830]: No new frame data for 1 consecutive attempts on appsrc 0x7f68019410
Jan  1 03:26:49 rk3576-buildroot user.warn mpp[13830]: No frame data available and no cached frame for appsrc 0x7f68019410 (attempt 1)
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6379, delay: 12153979 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] Pre-processing: 3.202 ms, Inference: 32.130 ms, Post-processing: 1.226 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Inference] [ThreadId] -1916581184, fetch 36.353 ms, infer 36.605 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Tracker] 0.193 ms
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [Publisher] frame id: 6380, delay: 12153974 ms, track objs: 2
Jan  1 03:26:49 rk3576-buildroot user.info video_service[13622]: [RTSP] Frame id: 6381, cost: 40 ms, rx: 35 ms, convert: 4 ms