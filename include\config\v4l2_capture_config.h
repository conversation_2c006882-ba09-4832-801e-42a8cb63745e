#ifndef V4L2_CAPTURE_CONFIG_H_
#define V4L2_CAPTURE_CONFIG_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <string>
#include <cstdint>

namespace V4L2Capture {
// V4L2设备配置
struct V4L2DeviceConfig {
    std::string topic_name = "video_frames";    // DDS input topic name or socket path (for DMA/SHMEM/FASTDDS)
    std::string transport_type = "DMA";        // DMA, SHMEM, FASTDDS
    int domain_id = 0;
    int max_samples = 3;

    std::string device_path;
    uint32_t width;
    uint32_t height;
    uint32_t pixel_format;
    uint32_t fps;
    uint32_t buffer_count;
};
}

#ifdef __cplusplus
}
#endif

#endif // V4L2_CAPTURE_CONFIG_H_