cmake_minimum_required(VERSION 3.16)
project(botsort VERSION 1.0 LANGUAGES CXX)

# Collect all source files
file(GLOB_RECURSE SOURCES "${PROJECT_SOURCE_DIR}/src/*.cpp")
list(REMOVE_ITEM SOURCES "${PROJECT_SOURCE_DIR}/src/ReID.cpp" 
                         "${PROJECT_SOURCE_DIR}/src/TRT_InferenceEngine/TensorRT_InferenceEngine.cpp")

# 创建对象库
add_library(${PROJECT_NAME} STATIC ${SOURCES})

# 设置属性
set_target_properties(${PROJECT_NAME} PROPERTIES
    POSITION_INDEPENDENT_CODE ON
)

# 查找依赖
find_package(OpenCV REQUIRED)
find_package(Eigen3 REQUIRED)

find_library(RGA_LIBRARY NAMES rga PATHS /usr/lib)
find_path(RGA_LIBRARY_INCLUDE_DIRS NAMES rga.h PATHS /usr/include/rga)
if(RGA_LIBRARY AND RGA_LIBRARY_INCLUDE_DIRS)
    set(RGA_FOUND TRUE)
    add_definitions(-DRGA_ACCELERATOR)
endif()


# 设置包含目录和链接库
target_include_directories(${PROJECT_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        ${RGA_LIBRARY_INCLUDE_DIRS}
)

target_link_libraries(${PROJECT_NAME}
    PUBLIC
        ${OpenCV_LIBS}
        Eigen3::Eigen
        ${RGA_LIBRARY}
)

#include(GNUInstallDirs)
## 安装规则
#install(TARGETS ${PROJECT_NAME}
#    EXPORT ${PROJECT_NAME}Config
#    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
#    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
#    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
#    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
#)
#install(DIRECTORY include/
#    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/${PROJECT_NAME}
#    FILES_MATCHING PATTERN "*.h"
#)
#
#install(EXPORT ${PROJECT_NAME}Config
#    FILE ${PROJECT_NAME}Config.cmake
#    NAMESPACE ${PROJECT_NAME}::
#    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}
#)
