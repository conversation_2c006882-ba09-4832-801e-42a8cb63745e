#ifndef RTSP_RKNN_CONFIG_H__
#define RTSP_RKNN_CONFIG_H__

#include <cstdint>
#include <string>
#include <opencv2/opencv.hpp>
#include <rga/im2d.hpp>
#include <rga/rga.h>

// RTSP RKNN tracking configuration
struct RTSPRKNNConfig {
    // RTSP configuration
    std::string url = "";                                  // RTSP stream URL
    std::string rtsp_transport = "udp";                    // RTSP transport: udp, tcp
    int rtsp_timeout = 5000000;                           // RTSP timeout in microseconds (5 seconds)
    std::string decode_method = "mpp";                     // Decode method: mpp, gstreamer

    // Output configuration
    std::string output_topic_name = "rtsp_rknn_tracked_frames";  // Output topic name after tracking
    std::string output_transport_type = "DMA";             // Output transport type: DMA, SHMEM, FASTDDS
    int output_ring_buffer_size = 3;                       // Ring buffer size
    int output_format = V4L2_PIX_FMT_BGR24;               // Output pixel format
    int output_width = 1920;                               // Output frame width
    int output_height = 1080;                              // Output frame height

    // FastDDS configuration (when using FASTDDS transport)
    int fastdds_domain_id = 0;                             // FastDDS domain ID
    int fastdds_max_samples = 5;                           // FastDDS max samples

    // RKNN model configuration
    std::string model_path = "/opt/video_service/models/yolo11s_rk3576.rknn";    // RKNN model file path
    std::string chip = "rk3576";                           // Chip type: rk3588, rk3576, etc
    
    // Detection configuration
    float box_threshold = 0.5f;                            // Bounding box threshold
    float nms_threshold = 0.45f;                           // NMS threshold
    
    // Tracking configuration
    int track_buffer = 60;                                 // Track buffer size
    int frame_rate = 30;                                   // Frame rate for tracking
    
    // Visualization configuration
    bool draw_boxes = true;                                // Draw bounding boxes on output
    bool draw_labels = true;                               // Draw labels on output
    bool draw_track_ids = true;                            // Draw track IDs on output
    
    // Debug options
    int debug_level = 2;                                   // Debug level (0-5)
    int stats_interval = 10;                               // Statistics print interval (seconds)
};

#endif // RTSP_RKNN_CONFIG_H__
