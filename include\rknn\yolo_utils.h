#ifndef _YOLO_UTILS_H_
#define _YOLO_UTILS_H_

#include <stdint.h>
#include <math.h>
#include <vector>
#include <omp.h>
#include <algorithm> // for std::sort
#include <numeric>
#include <stdlib.h>
#include <set>
#include <iostream>
#include "common.h"
#include <opencv2/opencv.hpp>
#include <im2d.hpp>
#include <RgaUtils.h>
#include <cstring>

#define OBJ_NAME_MAX_SIZE 64
#define OBJ_NUMB_MAX_SIZE 128
#define OBJ_CLASS_NUM 80
#define NMS_THRESH 0.45
#define BOX_THRESH 0.25

typedef struct {
    int left;
    int top;
    int right;
    int bottom;
} image_rect_t;

typedef struct {
    image_rect_t box;
    float prop;
    int cls_id;
} object_detect_result;

typedef struct {
    int id;
    int count;
    object_detect_result results[OBJ_NUMB_MAX_SIZE];
} object_detect_result_list;

typedef struct {
    int x_pad;
    int y_pad;
    float scale;
} letterbox_t;

inline int32_t __clip(float val, float min, float max)
{
    return (int32_t)(val <= min ? min : (val >= max ? max : val));
}

inline int8_t qnt_f32_to_affine(float f32, int32_t zp, float scale)
{
    float dst_val = (f32 / scale) + zp;
    return (int8_t)__clip(dst_val, -128.0f, 127.0f);
}

inline float deqnt_affine_to_f32(int8_t qnt, int32_t zp, float scale) { return ((float)qnt - (float)zp) * scale; }

inline void compute_dfl(std::vector<float> & tensor, int dfl_len, float* box) {
    for (int b = 0; b < 4; b++) {
        float exp_sum = 0.0f;
        float acc_sum = 0.0f;
        for (int i = 0; i < dfl_len; i++) {
            float exp_val = exp(tensor[i + b * dfl_len]);
            exp_sum += exp_val;
            acc_sum += exp_val * i;
        }
        box[b] = acc_sum / exp_sum;
    }
}

inline int process_i8(int8_t* box_tensor, int32_t box_zp, float box_scale,
    int8_t* score_tensor, int32_t score_zp, float score_scale,
    int8_t* score_sum_tensor, int32_t score_sum_zp, float score_sum_scale,
    int grid_h, int grid_w, int stride, const int dfl_len,
    std::vector<float>& boxes,
    std::vector<float>& objProbs,
    std::vector<int>& classId,
    float threshold)
{
    int validCount = 0;
    int grid_len = grid_h * grid_w;
    int8_t score_thres_i8 = qnt_f32_to_affine(threshold, score_zp, score_scale);
    int8_t score_sum_thres_i8 = qnt_f32_to_affine(threshold, score_sum_zp, score_sum_scale);
    // 避免未使用参数警告
    (void)box_scale; // 保留参数用于后续可能的优化
    (void)score_scale; // 保留参数用于后续可能的优化

    for (int i = 0; i < grid_h; i++)
    {
        for (int j = 0; j < grid_w; j++)
        {
            int offset = i * grid_w + j;
            int max_class_id = -1;
            if ((score_sum_tensor != nullptr) && (score_sum_tensor[offset] < score_sum_thres_i8)) {
                continue;
            }
            int8_t max_score = -score_zp;
            for (int c = 0; c < OBJ_CLASS_NUM; c++) {
                if ((score_tensor[offset] > score_thres_i8) && (score_tensor[offset] > max_score))
                {
                    max_score = score_tensor[offset];
                    max_class_id = c;
                }
                offset += grid_len;
            }
            if (max_score > score_thres_i8) {
                offset = i * grid_w + j;
                float box[4];
                // 使用vector替代VLA以避免ISO C++警告
                std::vector<float> before_dfl(dfl_len * 4);
                for (int k = 0; k < dfl_len * 4; k++) {
                    before_dfl[k] = deqnt_affine_to_f32(box_tensor[offset], box_zp, box_scale);
                    offset += grid_len;
                }
                compute_dfl(before_dfl, dfl_len, box);
                float x1, y1, x2, y2, w, h;
                x1 = (-box[0] + j + 0.5) * stride;
                y1 = (-box[1] + i + 0.5) * stride;
                x2 = (box[2] + j + 0.5) * stride;
                y2 = (box[3] + i + 0.5) * stride;
                w = x2 - x1;
                h = y2 - y1;
                {
                    boxes.push_back(x1);
                    boxes.push_back(y1);
                    boxes.push_back(w);
                    boxes.push_back(h);
                    objProbs.push_back(deqnt_affine_to_f32(max_score, score_zp, score_scale));
                    classId.push_back(max_class_id);
                    validCount++;
                }
            }
        }
    }
    return validCount;
}

inline void quick_sort_indice_inverse(std::vector<float>& input, std::vector<int>& indices) {
    size_t n = input.size();
    if (indices.empty()) {
        indices.resize(n);
        std::iota(indices.begin(), indices.end(), 0);
    }
    std::vector<std::pair<float, int>> paired(n);
    for (size_t i = 0; i < n; ++i) {
        paired[i] = std::make_pair(input[i], indices[i]);
    }
    std::sort(paired.begin(), paired.end(), [](const std::pair<float, int>& a, const std::pair<float, int>& b) {
        return a.first > b.first;
        });
    for (size_t i = 0; i < n; ++i) {
        input[i] = paired[i].first;
        indices[i] = paired[i].second;
    }
}

inline float CalculateOverlap(float xmin0, float ymin0, float xmax0, float ymax0, float xmin1, float ymin1, float xmax1,
    float ymax1)
{
    float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
    float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
    float i = w * h;
    float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
    return u <= 0.f ? 0.f : (i / u);
}

inline int nms(int validCount, std::vector<float>& outputLocations, std::vector<int> classIds, std::vector<int>& order,
    int filterId, float threshold)
{
    for (int i = 0; i < validCount; ++i)
    {
        int n = order[i];
        if (n == -1 || classIds[n] != filterId)
        {
            continue;
        }
        float xmin0 = outputLocations[n * 4 + 0];
        float ymin0 = outputLocations[n * 4 + 1];
        float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
        float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];
        for (int j = i + 1; j < validCount; ++j)
        {
            int m = order[j];
            if (m == -1 || classIds[m] != filterId)
            {
                continue;
            }
            float xmin1 = outputLocations[m * 4 + 0];
            float ymin1 = outputLocations[m * 4 + 1];
            float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
            float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];
            float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);
            if (iou > threshold)
            {
                order[j] = -1;
            }
        }
    }
    return 0;
}

inline int clamp(float val, int min, int max) { return val > min ? (val < max ? val : max) : min; }

inline int adaptive_resize(const cv::Mat& src, int src_format, int dst_format, const cv::Size & target_size, uint8_t* dst_virtual_addr, letterbox_t* letterbox, uint8_t /*fill_color*/, int interpolation = cv::INTER_LINEAR) {
    if (src.empty() || dst_virtual_addr == nullptr || letterbox == nullptr) {
        LOG_E("Invalid input parameters, src: %p, dst: %p, letterbox: %p", src.data, dst_virtual_addr, letterbox);
        return -1;
    }
    int src_width = src.cols;
    int src_height = src.rows;
    if (src_width == target_size.width && src_height == target_size.height) {
        LOG_E("No scaling needed.");
        memcpy(dst_virtual_addr, src.data, target_size.width * target_size.height * 3);
        letterbox->scale = 1.0;
        letterbox->x_pad = 0;
        letterbox->y_pad = 0;
        return 0;
    }
    double scale = std::min(static_cast<double>(target_size.width) / src_width, static_cast<double>(target_size.height) / src_height);
    int new_width = static_cast<int>(src_width * scale);
    int new_height = static_cast<int>(src_height * scale);
    if (new_width == target_size.width && new_height == target_size.height) {
        LOG_E("No padding needed. just scale");
        rga_buffer_t src_buf = wrapbuffer_virtualaddr_t(src.data, src_width, src_height, src_width, src_height, src_format);
        rga_buffer_t dst_buf = wrapbuffer_virtualaddr_t(dst_virtual_addr, target_size.width, target_size.height, target_size.width, target_size.height, dst_format);
        IM_STATUS status = imresize(src_buf, dst_buf, scale, scale, interpolation);
        if (status != IM_STATUS_SUCCESS) {
            LOG_E("RGA resize failed: %s", imStrError_t(status));
            return -1;
        }
        letterbox->scale = scale;
        letterbox->x_pad = 0;
        letterbox->y_pad = 0;
        return 0;
    }
    int pad_left = (target_size.width - new_width) / 2;
    int pad_top = (target_size.height - new_height) / 2;
    rga_buffer_t src_buf = wrapbuffer_virtualaddr_t(src.data, src_width, src_height, src_width, src_height, src_format);
    rga_buffer_t dst_buf = wrapbuffer_virtualaddr_t(dst_virtual_addr + pad_left * 3 + pad_top * target_size.width * 3, target_size.width, new_height, target_size.width, new_height, dst_format);
    IM_STATUS status = imresize(src_buf, dst_buf, scale, scale, interpolation);
    if (status != IM_STATUS_SUCCESS) {
        LOG_E("RGA resize failed: %s", imStrError_t(status));
        return -1;
    }
    letterbox->scale = scale;
    letterbox->x_pad = pad_left;
    letterbox->y_pad = pad_top;
    return 0;
}

inline int adaptive_letterbox(const cv::Mat& src, const cv::Size & target_size, uint8_t* dst_virtual_addr, letterbox_t* letterbox)
{
    const uint8_t fill_color = 114;
    int interpolation = cv::INTER_LINEAR;
    return adaptive_resize(src, RK_FORMAT_BGR_888, RK_FORMAT_RGB_888, target_size, dst_virtual_addr, letterbox, fill_color, interpolation);
}

#endif