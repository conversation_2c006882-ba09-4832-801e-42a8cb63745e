#pragma once
#include <algorithm> // std::min, std::max
#include <condition_variable>
#include <cstdint>  // uint32_t, int32_t
#include <cstring>  // std::memset, std::memcpy
#include <iostream> // std::cout, std::endl
#include <memory>
#include <mutex>
#include <vector>
#include "../common.h"


template <typename T>
class rknn_buffer {
private:
  /* data */
  int state{0};                             // 0: 空闲, 1: 处理中, 2: 就绪

public:
  T data;                                   // 数据缓冲区

  bool is_ready() const {
    return state == 2; // 检查是否处于就绪状态
  }

  bool is_idle() const {
    return state == 0; // 检查是否处于空闲状态
  }

  void set_ready() {
    state = 2; // 设置为就绪状态
  }

  void set_processing() {
    state = 1; // 设置为处理中状态
  }

  void set_idle() {
    state = 0; // 设置为空闲状态
  }

  rknn_buffer &operator=(const rknn_buffer &other) {
    if (this != &other) { // 防止自赋值
      state = other.state;     // 复制状态
      data = other.data; // 复制输出数据
    }
    return *this;
  }

  rknn_buffer &operator=(rknn_buffer &&other) noexcept {
    state = other.state;                // 移动状态
    data = std::move(data); // 移动输出数据
    return *this;
  }

  rknn_buffer() : state(0) {} // 默认构造函数

  rknn_buffer(rknn_buffer &&other) noexcept
      : state(other.state),
        data(std::move(other.data)) {}

  ~rknn_buffer() = default; // 默认析构函数
};

template <typename T>
class rw_bufferpool {
private:
  /* data */
  const int buffer_pool_size;             // 缓冲区池大小
  std::vector<rknn_buffer<T>> buffer;    // 数据缓冲区
  uint32_t write_idx{0};                  // 当前写入缓冲区索引
  uint32_t read_idx{0};                   // 当前读取缓冲区索引
  std::unique_ptr<std::mutex> pool_mutex; // 互斥锁
  std::condition_variable data_cv; // 条件变量，用于通知数据可用

public:
  rw_bufferpool(int pool_size = 2)
      : buffer_pool_size(pool_size),
      buffer(pool_size), 
      write_idx(0),
      read_idx(0),
      pool_mutex(std::make_unique<std::mutex>()) {}

  rw_bufferpool(rw_bufferpool &&other) = default;     // 移动构造函数
  rw_bufferpool(const rw_bufferpool &other) = delete; // 禁用拷贝构造函数
  rw_bufferpool &operator=(const rw_bufferpool &other) = delete; // 禁用拷贝赋值运算符
  ~rw_bufferpool() = default; // 默认析构函数

  rknn_buffer<T> *get_idle_buffer() {
    std::lock_guard<std::mutex> lock(*pool_mutex.get()); // 锁定缓冲区池
    for(int i = 0; i < buffer_pool_size; ++i) {
      auto &buf = this->buffer[write_idx];
      write_idx = (write_idx + 1) % buffer_pool_size; // 更新写入索引
      if (buf.is_idle()) // 如果缓冲区处于空闲状态
      {
        buf.set_processing(); // 设置为处理中状态
        return &buf;
      }
    }
    return nullptr; // 如果没有可用的缓冲区，返回nullptr
  }

  void set_buffer_ready(rknn_buffer<T> *buf) {
    if (buf == nullptr) {
      LOG_E("Buffer is null!");
      return;
    }
    std::lock_guard<std::mutex> lock(*pool_mutex.get()); // 锁定缓冲区池
    buf->set_ready(); // 设置为就绪状态
    data_cv.notify_one(); // 通知读取线程
  }

  rknn_buffer<T> * get_ready_buffer() {
    std::unique_lock<std::mutex> lock(*pool_mutex.get()); // 锁定缓冲区池
    data_cv.wait(lock, [this] { return this->buffer[read_idx].is_ready(); }); // 等待缓冲区就绪
    auto &buf = this->buffer[read_idx];
    read_idx = (read_idx + 1) % buffer_pool_size; // 更新写入索引
    buf.set_processing(); // 设置为处理中状态
    return &buf;
  }

  void set_buffer_idle(rknn_buffer<T> *buf) {
    if (buf == nullptr) {
      LOG_E("Buffer is null!");
      return;
    }
    std::lock_guard<std::mutex> lock(*pool_mutex.get()); // 锁定缓冲区池
    buf->set_idle(); // 设置为空闲状态
  }

  int get_buffer_pool_size() const {
    return buffer_pool_size;
  }                                               // 获取缓冲区池大小
  int get_write_idx() const { return write_idx; } // 获取当前写入索引
  int get_read_idx() const { return read_idx; }   // 获取当前读取索引
};