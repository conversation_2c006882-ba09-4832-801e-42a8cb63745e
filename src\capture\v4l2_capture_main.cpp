#include "../include/common.h"
#include "../include/transport/video_transport_interface.h"
#include "../include/transport/fastdds_video_transport.h"
#include "../include/transport/shared_buffer_publisher.h"
#include "../include/config/v4l2_capture_config.h"
#include "../include/capture/v4l2_capture_interface.h"
#include "../include/capture/v4l2_utils.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <atomic>
#include <fstream>
#include <json/json.h>


using namespace video_transport;
using namespace V4L2Capture;

static std::atomic<bool> running(true);
static std::unique_ptr<IV4L2CaptureDevice> g_device;
static std::unique_ptr<IVideoPublisher> g_publisher;

void signal_handler(int sig) {
    LOG_W("[v4l2_capture_main] Received signal %d, stopping...", sig);
    running = false;
}

uint32_t string_to_pixelformat(const std::string& format_str) {
    if (format_str.length() != 4) {
        return 0;
    }
    return (format_str[0]) | (format_str[1] << 8) | (format_str[2] << 16) | (format_str[3] << 24);
}

bool load_config(const std::string& config_file, struct V4L2DeviceConfig& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        LOG_E("Failed to open config file: %s ", config_file.c_str());
        return false;
    }

    Json::Value root;
    Json::CharReaderBuilder builder;
    Json::parseFromStream(builder, file, &root, nullptr);

    // 注意：这里只在JSON文件中存在相应键时才更新配置，保持默认配置作为最低优先级
    if (root.isMember("device")) {
        config.device_path = root["device"].asString();
    }
    if (root.isMember("width")) {
        config.width = root["width"].asUInt();
    }
    if (root.isMember("height")) {
        config.height = root["height"].asUInt();
    }
    if (root.isMember("pixel_format")) {
        std::string format_str = root["pixel_format"].asString();
        config.pixel_format = string_to_pixelformat(format_str);
        LOG_I("Pixel format: %s, 0x%08x", format_str.c_str(), config.pixel_format);
    }
    if (root.isMember("fps")) {
        config.fps = root["fps"].asUInt();
    }
    if (root.isMember("buffer_count")) {
        config.buffer_count = root["buffer_count"].asUInt();
    }
    if (root.isMember("topic_name")) {
        config.topic_name = root["topic_name"].asString();
    }
    if (root.isMember("transport_type")) {
        config.transport_type = root["transport_type"].asString();
    }
    if (root.isMember("domain_id")) {
        config.domain_id = root["domain_id"].asInt();
    }
    if (root.isMember("max_samples")) {
        config.max_samples = root["max_samples"].asInt();
    }

    return true;
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: /opt/video_service/config/main_v4l2_capture.json)\n"
              << "  -d, --device PATH     V4L2 device path (default: /dev/video0)\n"
              << "  -w, --width WIDTH     Video width (default: 1920)\n"
              << "  -h, --height HEIGHT   Video height (default: 1080)\n"
              << "  -f, --fps FPS         Frame rate (default: 30)\n"
              << "  --format FORMAT       Pixel format (default: YUYV)\n"
              << "  --topic TOPIC         DDS topic name (default: main_video_frames)\n"
              << "  --buffer-count COUNT  Buffer count (default: 3)\n"
              << "  --transport TYPE      Transport type (FASTDDS, DMA, SHMEM) (default: DMA)\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    struct V4L2DeviceConfig config = {
        "main_video_frames", "DMA", 0, 3, "/dev/video0", 1920, 1080, V4L2_PIX_FMT_YUYV, 30, 3, 
    };

    std::string config_file = "/opt/video_service/config/main_v4l2_capture.json";

    // 2. JSON配置文件（中等优先级）
    load_config(config_file, config);

    // 3. 命令行参数（最高优先级）
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                config_file = argv[++i];
                // 重新加载配置文件以确保命令行指定的配置文件优先
                load_config(config_file, config);
            }
        } else if (arg == "-d" || arg == "--device") {
            if (i + 1 < argc) {
                config.device_path = argv[++i];
            }
        } else if (arg == "-w" || arg == "--width") {
            if (i + 1 < argc) {
                config.width = std::stoi(argv[++i]);
            }
        } else if (arg == "-h" || arg == "--height") {
            if (i + 1 < argc) {
                config.height = std::stoi(argv[++i]);
            }
        } else if (arg == "-f" || arg == "--fps") {
            if (i + 1 < argc) {
                config.fps = std::stoi(argv[++i]);
            }
        } else if (arg == "--format") {
            if (i + 1 < argc) {
                std::string format_str = argv[++i];
                config.pixel_format = string_to_pixelformat(format_str);
            }
        } else if (arg == "--topic") {
            if (i + 1 < argc) {
                config.topic_name = argv[++i];
            }
        } else if (arg == "--buffer-count") {
            if (i + 1 < argc) {
                config.buffer_count = std::stoi(argv[++i]);
            }
        } else if (arg == "--transport") {
            if (i + 1 < argc) {
                config.transport_type = argv[++i];
            }
        } else if (arg == "--help") {
            print_usage(argv[0]);
            return 0;
        }
    }

    LOG_I("=== V4L2 Video Capture ===");
    LOG_I("Device: %s", config.device_path.c_str());
    LOG_I("Resolution: %dx%d", config.width, config.height);
    LOG_I("Pixel Format: %s", V4L2FormatUtils::pixelformat_to_string(config.pixel_format).c_str());
    LOG_I("FPS: %d", config.fps);
    LOG_I("Buffer Count: %d", config.buffer_count);
    LOG_I("Transport Type: %s", config.transport_type.c_str());

    // Create transport publisher
    TransportConfig transport_config;
    transport_config.topic_name = config.topic_name;
    transport_config.type = config.transport_type == "FASTDDS" ? TransportType::FASTDDS : 
        (config.transport_type == "DMA" ? TransportType::DMA : TransportType::SHMEM);

    if (transport_config.type == TransportType::FASTDDS) {
        transport_config.domain_id = config.domain_id;
        transport_config.max_samples = config.max_samples;
        transport_config.timeout_ms = 1000;
    } else {
        transport_config.buffer_size = V4L2FormatUtils::calculate_frame_size(config.pixel_format, config.width, config.height);
        transport_config.ring_buffer_size = config.buffer_count;
        transport_config.timeout_ms = 1000;
    }

    try {
        g_publisher = VideoTransportFactory::create_publisher(transport_config);
        if (!g_publisher) {
            LOG_E("Failed to create video publisher");
            return 1;
        }
    } catch (const std::exception& e) {
        LOG_E("Exception creating publisher: %s", e.what());
        return 1;
    }

    g_device = V4L2DeviceFactory::create_and_configure(config, g_publisher.get());
    if (!g_device) {
        LOG_E("Failed to create and configure V4L2 device");
        g_publisher->cleanup();
        return 1;
    }

    // Start streaming
    if (!g_device->start_streaming()) {
        LOG_E("Failed to start streaming");
        g_device->close_device();
        g_publisher->cleanup();
        return 1;
    }

    LOG_I("Video capture started...");

    // Main capture loop
    uint64_t frame_count = 0;
    const uint32_t interval_ms = 1000 / config.fps;
    auto prev_30frame_time = std::chrono::steady_clock::now();
    const uint32_t frames_to_print_fps = 30;

    while (running) {
        V4L2Frame frame;
        auto start_ts = std::chrono::steady_clock::now();
        if (g_device->capture_frame(frame, transport_config.timeout_ms)) {
            BufferResult result = g_publisher->publish_buffer(frame.publisher_data);
            g_device->release_frame(frame);
            if (result == BufferResult::SUCCESS) {
                auto end_ts = std::chrono::steady_clock::now();
                auto capture_interval = std::chrono::duration_cast<std::chrono::milliseconds>(end_ts - start_ts).count();
                auto sleep_interval = interval_ms - capture_interval;
                // LOG_I("[v4l2_capture_main] Capture interval: %ld ms, sleep for %ld ms", capture_interval, sleep_interval);
                if (capture_interval < interval_ms) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_interval));
                }

                if (frame_count++ % frames_to_print_fps == 0) {
                    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(end_ts - prev_30frame_time).count();
                    prev_30frame_time = end_ts;

                    if (elapsed > 0) {
                        double fps = static_cast<double>(frames_to_print_fps) / elapsed;
                        LOG_I("[v4l2_capture_main] Topic: %s, Frames: %lu, FPS: %.1f", config.topic_name.c_str(), frame_count, fps);
                    }
                }
            } else if (result != BufferResult::TIMEOUT) {
                LOG_E("[v4l2_capture_main] Publisher Failed to publish frame: %d", static_cast<int>(result));
            }
        }
    }

    // Cleanup
    g_device->stop_streaming();
    g_device->close_device();
    g_publisher->cleanup();

    LOG_I("Captured %lu frames", frame_count);
    LOG_I("Video capture stopped.");

    return 0;
}