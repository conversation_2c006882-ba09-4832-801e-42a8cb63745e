#ifndef AI_CONFIG_H
#define AI_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#include <cstdint>
#include <string>

// AI processing configuration
struct AIConfig {
    std::string model_path = "model.onnx";
    std::string engine_type = "onnx";      // tensorrt, onnx, openvino
    int batch_size = 1;
    bool use_gpu = true;
    float confidence_threshold = 0.5f;
    int max_detections = 100;

    // Preprocessing config
    int input_width = 640;
    int input_height = 640;
    bool normalize = true;
    std::string color_format = "RGB";

    // Postprocessing config
    float nms_threshold = 0.4f;
    float score_threshold = 0.25f;
    int max_output_boxes = 100;
    bool class_agnostic_nms = false;

    // DDS config
    std::string input_topic = "AI_Frames";
    std::string output_topic = "AI_Results";
    int domain_id = 0;
    int input_max_samples = 3;
    int output_max_samples = 3;

    // Performance config
    int thread_priority = 70;
    int process_interval_ms = 0;    // 0=process every frame
    int stats_interval_sec = 10;
};

#ifdef __cplusplus
}
#endif

#endif // AI_CONFIG_H