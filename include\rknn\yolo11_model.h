#pragma once
#include <opencv2/opencv.hpp>
#include "rknn_model.h"
#include "yolo_utils.h"
#include "../common.h"

//Input data type: cv::Mat
//Output data type: object_detect_result_list
class yolo11_model : public rknn_model<object_detect_result_list>{
    private:
        float conf_threshold;
        float nms_threshold;
        letterbox_t letter_box;
    public:
        yolo11_model(float conf_threshold, float nms_threshold);
        int pre_process(cv::Mat & frame);
        int post_process(object_detect_result_list & results);
        ~yolo11_model();
};

