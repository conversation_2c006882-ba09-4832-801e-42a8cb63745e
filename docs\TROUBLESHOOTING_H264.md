# H.264 Streaming Troubleshooting Guide

This guide helps troubleshoot H.264 stream parsing errors in the cloud streamer.

## Common Error Messages

### 1. "Failed to parse stream" Error

**Error Log:**
```
GStreamer error: Failed to parse stream
Debug info: ../libs/gst/base/gstbaseparse.c(3030): gst_base_parse_check_sync (): /GstPipeline:pipeline0/GstH264Parse:h264parse0
```

**Possible Causes:**
- H.264 stream format mismatch (byte-stream vs AVC)
- Corrupted or incomplete NAL units
- Missing SPS/PPS headers
- Incorrect stream alignment

**Solutions:**
1. **Check stream format**: Use the debug tool to analyze your stream
   ```bash
   ./build/tools/debug_h264_stream DMA main_video_frames
   ```

2. **Verify NAL unit structure**: Ensure your H.264 stream has proper start codes (0x000001 or 0x00000001)

3. **Check for SPS/PPS**: Make sure your stream includes SPS and PPS NAL units

### 2. "Internal data stream error" Error

**Error Log:**
```
GStreamer error: Internal data stream error.
Debug info: ../libs/gst/base/gstbasesrc.c(3132): gst_base_src_loop (): /GstPipeline:pipeline0/GstAppSrc:source: streaming stopped, reason error (-5)
```

**Possible Causes:**
- Pipeline element linkage failure
- Buffer flow issues
- Caps negotiation failure

**Solutions:**
1. **Enable verbose logging**:
   ```bash
   ./build/src/streaming/cloud_streamer_main -c config/main_cloud_streamer.json --verbose
   ```

2. **Check pipeline configuration**: Verify GStreamer elements are properly linked

## Debug Tools

### 1. H.264 Stream Debug Tool

Analyzes the first 10 frames of your video stream:

```bash
# For DMA transport
./build/tools/debug_h264_stream DMA main_video_frames

# For SHMEM transport  
./build/tools/debug_h264_stream SHMEM main_video_frames

# For FastDDS transport
./build/tools/debug_h264_stream FASTDDS main_video_frames
```

**Output Analysis:**
- **Detected format**: Should show "byte-stream" for Annex-B format
- **Valid H.264**: Should show "Yes" if NAL units are properly formatted
- **NAL Unit Analysis**: Shows types of NAL units found (SPS, PPS, IDR, etc.)

### 2. Debug Script

Use the comprehensive debug script:

```bash
./scripts/debug_streaming.sh
```

This script provides:
- Stream format analysis
- System dependency checks
- GStreamer pipeline testing
- Configuration review

## Configuration Fixes

### 1. Flexible H.264 Caps

The updated implementation now uses flexible caps that allow h264parse to auto-detect the stream format:

```cpp
// Old (rigid)
caps = gst_caps_new_simple("video/x-h264",
    "stream-format", G_TYPE_STRING, "byte-stream",
    "alignment", G_TYPE_STRING, "au",
    nullptr);

// New (flexible)
caps = gst_caps_new_simple("video/x-h264",
    "width", G_TYPE_INT, config_.width,
    "height", G_TYPE_INT, config_.height,
    "framerate", GST_TYPE_FRACTION, config_.fps, 1,
    nullptr);
```

### 2. Enhanced H.264 Parser Configuration

The pipeline now includes enhanced parsing options:

```cpp
pipeline << "! h264parse config-interval=1 "
         << "disable-passthrough=true "
         << "split-packetized=true ";
```

**Parameters:**
- `config-interval=1`: Insert SPS/PPS before every IDR frame
- `disable-passthrough=true`: Force parsing even if caps match
- `split-packetized=true`: Split packetized streams into individual NAL units

### 3. Stream Validation

The implementation now validates H.264 data before pushing to GStreamer:

```cpp
if (!validate_h264_data(static_cast<const uint8_t*>(frame.data_ptr), frame.meta.data_size)) {
    LOG_W("Invalid H.264 data format detected, frame_id: %lu", frame.meta.frame_id);
    return true; // Skip frame instead of failing
}
```

## System Requirements

### GStreamer Plugins

Ensure these GStreamer plugins are installed:

```bash
# Check plugin availability
gst-inspect-1.0 h264parse
gst-inspect-1.0 rtmpsink  
gst-inspect-1.0 appsrc
gst-inspect-1.0 queue
```

### Test Pipeline

Test basic H.264 parsing with:

```bash
gst-launch-1.0 -v videotestsrc num-buffers=10 ! \
    video/x-raw,width=1280,height=720,framerate=30/1 ! \
    x264enc bitrate=2000 ! \
    h264parse ! \
    fakesink
```

## Recovery Strategies

### 1. Automatic Error Recovery

The implementation includes automatic recovery for parsing errors:

- Skip invalid frames instead of stopping the pipeline
- Attempt to flush and restart appsrc on persistent errors
- Dynamic caps adjustment based on detected stream format

### 2. Pipeline Restart

For persistent errors, the system can automatically restart the pipeline:

```cpp
if (pipeline_errors_.load() > 5) {
    LOG_E("Too many pipeline errors, consider restarting");
    if (should_auto_reconnect()) {
        schedule_pipeline_restart();
    }
}
```

## Best Practices

1. **Always validate input data** before pushing to GStreamer
2. **Use flexible caps** to allow format auto-detection
3. **Enable verbose logging** during development and debugging
4. **Monitor error patterns** to identify systematic issues
5. **Implement graceful error recovery** instead of hard failures

## Getting Help

If you continue to experience issues:

1. Run the debug tools to collect detailed information
2. Check the system logs for additional error details
3. Verify your H.264 encoder configuration
4. Test with a known-good H.264 stream

For additional support, provide:
- Complete error logs with verbose output enabled
- Output from the debug tools
- Your configuration file
- Information about your H.264 encoder setup
