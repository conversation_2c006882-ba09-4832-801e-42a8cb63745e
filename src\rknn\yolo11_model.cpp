
#include "../../include/rknn/yolo11_model.h"


yolo11_model::yolo11_model(float conf_threshold, float nms_threshold) : rknn_model(), conf_threshold(conf_threshold), nms_threshold(nms_threshold) {}
yolo11_model::~yolo11_model() {
    LOG_I("yolo11_model destroyed");
}

int yolo11_model::pre_process(cv::Mat & frame) {
    const cv::Size target_size(get_model_width(), get_model_height());
    memset((void *)&letter_box, 0, sizeof(letterbox_t));

#if defined(ENABLE_ZERO_COPY)
    auto *rknn_tensor_input_mem = get_input_mem_ptr(0);
    int result = adaptive_letterbox(frame, target_size, (uint8_t *)rknn_tensor_input_mem->virt_addr, &letter_box);
    if (result != 0)
    {
        LOG_E("Failed to letterbox the image!");
        return -1;
    }
#else
    rknn_input * inputs = get_input_ptr();
    const rknn_input_output_num & io_num = get_io_num();
    int result = adaptive_letterbox(frame, target_size, (uint8_t *)inputs[0].buf, &letter_box);
    if (result != 0)
    {
        LOG_E("Failed to letterbox the image!");
        return -1;
    }
    rknn_inputs_set(get_context(), io_num.n_input, inputs);
#endif

    return 0;
}

int NC1HWC2_i8_to_NCHW_i8(const int8_t* src, int8_t* dst, int* dims, int channel, int h, int w, int zp, float scale) {
    int batch = dims[0];
    int C1 = dims[1];
    int C2 = dims[4];
    int hw_src = dims[2] * dims[3];
    int hw_dst = h * w;
    (void)zp; // 未使用参数警告
    (void)scale; // 未使用参数警告

    for (int i = 0; i < batch; i++) {
        const int8_t* src_b = src + i * C1 * hw_src * C2;
        int8_t* dst_b = dst + i * channel * hw_dst;
        for (int c = 0; c < channel; ++c) {
            int           plane = c / C2;
            const int8_t* src_bc = src_b + plane * hw_src * C2;
            int           offset = c % C2;
            // Copy data from src to dst in a contiguous manner
            for (int cur_hw = 0; cur_hw < hw_dst; ++cur_hw) {
                dst_b[c * hw_dst + cur_hw] = src_bc[C2 * cur_hw + offset];
            }
        }
    }
    return 0;
}

int yolo11_model::post_process(object_detect_result_list & results)
{
    // 定义用于存储过滤后的边界框、对象概率和类别ID的向量
    std::vector<float> filterBoxes;
    std::vector<float> objProbs;
    std::vector<int> classId;
    int validCount = 0; // 有效检测框的数量
    int stride = 0; // 步长，用于计算网格大小
    int grid_h = 0; // 网格高度
    int grid_w = 0; // 网格宽度
    int model_in_w = get_model_width(); // 模型输入宽度
    int model_in_h = get_model_height(); // 模型输入高度
    const rknn_input_output_num& io_num = get_io_num();
    rknn_output * rknn_outputs = get_output_ptr();
    const std::vector<rknn_tensor_attr>& output_attrs = get_output_attrs();
    // 默认有3个输出分支
    int dfl_len = output_attrs[0].dims[1] / 4; // 计算每个分支的DFL长度
    int output_per_branch = io_num.n_output / 3; // 每个分支的输出数量
    
#if defined(ENABLE_ZERO_COPY)
    const std::vector<rknn_tensor_attr>& output_native_attrs = get_output_native_attrs();
    const std::vector<rknn_tensor_mem*>& output_mems = get_output_mems();
    for (uint32_t i = 0; i < io_num.n_output; ++i)
    {
        int channel = output_attrs[i].dims[1];
        int h = output_attrs[i].n_dims > 2 ? output_attrs[i].dims[2] : 1;
        int w = output_attrs[i].n_dims > 3 ? output_attrs[i].dims[3] : 1;
        int zp = output_native_attrs[i].zp;
        float scale = output_native_attrs[i].scale;

        // NC1HWC2 格式转换为 NCHW 格式
        if (output_native_attrs[i].fmt == RKNN_TENSOR_NC1HWC2)
        {
            NC1HWC2_i8_to_NCHW_i8((int8_t *)output_mems[i]->virt_addr, (int8_t *)rknn_outputs[i].buf,
                                (int *)output_native_attrs[i].dims, channel, h, w, zp, scale);
        }
        else
        {
            memcpy(rknn_outputs[i].buf, output_mems[i]->virt_addr, rknn_outputs[i].size);
        }
    }
#else
    rknn_outputs_get(get_context(), io_num.n_output, rknn_outputs, nullptr);
#endif


    /*
    // 预估可能的最大有效检测框数量
    int max_valid_boxes_per_branch = 0;
    for (int i = 0; i < 3; i++) {
        int grid_h = output_attrs[i * output_per_branch].dims[2];
        int grid_w = output_attrs[i * output_per_branch].dims[3];
        max_valid_boxes_per_branch += grid_h * grid_w;
    }

    // 预分配空间以容纳最大可能的有效检测框数量
    filterBoxes.reserve(max_valid_boxes_per_branch * 4); // 每个框有4个坐标值
    objProbs.reserve(max_valid_boxes_per_branch);        // 每个框有一个置信度
    classId.reserve(max_valid_boxes_per_branch);         // 每个框有一个类别ID
    */

    // #pragma omp parallel for schedule(auto) reduction(+:validCount)
    for (int i = 0; i < 3; i++)
    {
        void* score_sum = nullptr;
        int32_t score_sum_zp = 0;
        float score_sum_scale = 1.0;
        // 如果每个分支有3个输出，则获取score_sum的缓冲区、零点和缩放因子
        if (output_per_branch == 3) {
            score_sum = rknn_outputs[i * output_per_branch + 2].buf;
            score_sum_zp = output_attrs[i * output_per_branch + 2].zp;
            score_sum_scale = output_attrs[i * output_per_branch + 2].scale;
        }
        int box_idx = i * output_per_branch; // 当前分支的box输出索引
        int score_idx = i * output_per_branch + 1; // 当前分支的score输出索引
        grid_h = output_attrs[box_idx].dims[2]; // 获取当前分支的网格高度
        grid_w = output_attrs[box_idx].dims[3]; // 获取当前分支的网格宽度
        stride = model_in_h / grid_h; // 计算步长

        // 处理当前分支的输出，提取有效的边界框、对象概率和类别ID
        // #pragma omp critical
        validCount += process_i8((int8_t*)rknn_outputs[box_idx].buf, output_attrs[box_idx].zp, output_attrs[box_idx].scale,
            (int8_t*)rknn_outputs[score_idx].buf, output_attrs[score_idx].zp, output_attrs[score_idx].scale,
            (int8_t*)score_sum, score_sum_zp, score_sum_scale,
            grid_h, grid_w, stride, dfl_len,
            filterBoxes, objProbs, classId, conf_threshold);
    }

    // 如果没有检测到任何对象
    if (validCount <= 0)
    {
        return 0; // 直接返回0，表示没有检测到对象
    }

    // 创建一个索引数组，用于后续的排序和NMS操作
    std::vector<int> indexArray;
    std::iota(indexArray.begin(), indexArray.end(), 0); // 初始化索引数组

    // 对对象概率进行快速排序，并保存排序后的索引
    quick_sort_indice_inverse(objProbs, indexArray);

    // 获取所有检测到的类别ID的集合
    std::set<int> class_set(std::begin(classId), std::end(classId));

    // 对每个类别进行非极大值抑制（NMS）
    //#pragma omp parallel for
    for (int c : class_set)
    {
        nms(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
    }

    int last_count = 0; // 用于记录最终的有效检测框数量

    // 遍历所有有效的检测框，进行最终的处理
    //#pragma omp parallel for schedule(dynamic) reduction(+:last_count)
    for (int i = 0; i < validCount; ++i)
    {
        if (indexArray[i] == -1 || last_count >= OBJ_NUMB_MAX_SIZE)
        {
            continue; // 如果索引无效或已达到最大检测框数量，跳过
        }
        int n = indexArray[i]; // 获取当前检测框的索引
    
        // 计算检测框的坐标，并减去letterbox的填充
        float x1 = filterBoxes[n * 4 + 0] - letter_box.x_pad;
        float y1 = filterBoxes[n * 4 + 1] - letter_box.y_pad;
        float x2 = x1 + filterBoxes[n * 4 + 2];
        float y2 = y1 + filterBoxes[n * 4 + 3];
        int id = classId[n]; // 获取当前检测框的类别ID
        float obj_conf = objProbs[i]; // 获取当前检测框的对象概率

        // 将检测框的坐标转换为原始图像的坐标，并进行clamp操作，确保坐标在有效范围内
        results.results[last_count].box.left = (int)(clamp(x1, 0, model_in_w) / letter_box.scale);
        results.results[last_count].box.top = (int)(clamp(y1, 0, model_in_h) / letter_box.scale);
        results.results[last_count].box.right = (int)(clamp(x2, 0, model_in_w) / letter_box.scale);
        results.results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h) / letter_box.scale);
        results.results[last_count].prop = obj_conf; // 设置对象概率
        results.results[last_count].cls_id = id; // 设置类别ID
        // #pragma omp atomic
        last_count++; // 增加有效检测框的数量
    }
    results.count = last_count;
    return 0; // 返回0，表示处理成功
}
